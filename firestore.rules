rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read config documents
    match /config/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.email in [
        '<EMAIL>',
        '<EMAIL>'
      ];
    }
    
    // Allow public read access to wallpapers and authenticated write access
    match /wallpapers/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow users to read and write their own user documents
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow users to manage their own favorites
      match /favorites/{favoriteId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Allow authenticated users to read notification history
    match /notification_history/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.email in [
        '<EMAIL>',
        '<EMAIL>'
      ];
    }
    
    // Allow authenticated users to read live notifications and admins to write
    match /live_notifications/{document} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.email in [
        '<EMAIL>',
        '<EMAIL>'
      ];
    }
    
    // Deny all other access by default
    match /{document=**} {
      allow read, write: if false;
    }
  }
}