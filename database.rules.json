{"rules": {"wallpapers": {".read": true, ".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.token.email == '<EMAIL>' || root.child('admin_emails').child(auth.token.email.replace('.', ',')).exists())"}, "categories": {".read": true, ".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.token.email == '<EMAIL>' || root.child('admin_emails').child(auth.token.email.replace('.', ',')).exists())"}, "admin_emails": {".read": "auth != null", ".write": "auth != null && (auth.token.email == '<EMAIL>' || auth.token.email == '<EMAIL>')"}, "$other": {".read": false, ".write": false}}}