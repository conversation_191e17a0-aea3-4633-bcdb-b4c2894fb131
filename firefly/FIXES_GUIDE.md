# Wallpaper Loading & Notification Fixes Guide

## Issues Identified and Fixed

### 1. OAuth Access Token FormatException

**Problem**: The service account private key in `firebase_config.dart` contains literal `\n` characters instead of actual newlines, causing a `FormatException` when trying to parse the private key for OAuth token generation.

**Root Cause**: The private key was copied with escaped newlines (`\\n`) instead of actual newline characters.

**Fix Applied**:
- Modified `_getAccessToken()` method in `admin_notification_service.dart`
- Added proper parsing of the service account JSON
- Fixed private key format by replacing `\\n` with actual newlines
- Added better error logging and debugging

**Code Changes**:
```dart
// Parse the service account JSON and fix the private key format
final serviceAccountMap = jsonDecode(FirebaseConfig.serviceAccountKeyJson);

// Fix the private key by properly handling newlines
if (serviceAccountMap['private_key'] != null) {
  String privateKey = serviceAccountMap['private_key'];
  // Replace literal \n with actual newlines
  privateKey = privateKey.replaceAll('\\n', '\n');
  serviceAccountMap['private_key'] = privateKey;
}
```

### 2. Wallpaper Loading Issues After Upload

**Problem**: Newly uploaded wallpapers were not appearing in the app immediately after upload, requiring app restart.

**Root Cause**: 
- Race condition between Firebase write and read operations
- Insufficient debugging to track the upload and refresh process
- No verification that uploaded wallpapers were actually added to the local list

**Fix Applied**:
- Added delays after Firebase writes to ensure data consistency
- Enhanced debugging throughout the upload and refresh process
- Added verification steps to confirm wallpapers are properly loaded
- Improved error handling and logging

**Code Changes**:
```dart
// Add a small delay to ensure Firebase has processed the write
await Future.delayed(const Duration(milliseconds: 500));

// Verify the new wallpaper is in the list
final newWallpaper = _wallpapers.firstWhere(
  (w) => w.id == wallpaper.id,
  orElse: () => throw Exception('New wallpaper not found in list'),
);
print('✅ New wallpaper verified in list: ${newWallpaper.title}');
```

### 3. Background Notification Delivery

**Problem**: Notifications not being received when the app is in a non-running state.

**Root Cause**: 
- OAuth token generation was failing due to private key format issues
- Insufficient debugging in the FCM HTTP request process
- No fallback mechanism verification

**Fix Applied**:
- Fixed OAuth token generation (see issue #1)
- Enhanced FCM HTTP request debugging
- Added comprehensive error logging
- Verified Firestore fallback mechanism

## Testing the Fixes

### 1. Test OAuth Token Generation

Run the test app:
```bash
cd /Users/<USER>/Documents/wallpaper/firefly
flutter run test_fixes.dart
```

Click "Test Service Account Key Format" to verify:
- ✅ Service account JSON parses correctly
- ✅ Private key format is fixed
- ✅ Key starts with `-----BEGIN PRIVATE KEY-----`
- ✅ Key ends with `-----END PRIVATE KEY-----`

### 2. Test Notification Sending

In the test app, click "Test Notification Sending" to verify:
- ✅ OAuth access token is obtained
- ✅ FCM HTTP v1 request is successful
- ✅ Notification is sent to the `all_users` topic

### 3. Test Wallpaper Upload and Loading

1. **Upload a new wallpaper** through the admin panel
2. **Check the console logs** for:
   ```
   💾 Saving wallpaper to Firebase: [wallpaper_id]
   ✅ Wallpaper saved to Firebase successfully
   🔄 Refreshing wallpapers after upload...
   ✅ Wallpapers refreshed successfully
   ✅ New wallpaper verified in list: [wallpaper_title]
   ```
3. **Verify the wallpaper appears** in the app immediately
4. **Check notification logs** for:
   ```
   🚀 Starting FCM HTTP v1 request...
   🔑 Getting OAuth access token...
   ✅ OAuth access token obtained
   📋 Project ID: firefly-8cbce
   📝 Message converted to v1 format
   📋 Target topic: all_users
   🌐 Sending request to: https://fcm.googleapis.com/v1/projects/firefly-8cbce/messages:send
   ✅ FCM HTTP v1 request successful!
   📱 Notification sent to topic: all_users
   ```

### 4. Test Background Notifications

1. **Close the app completely** (not just minimize)
2. **Upload a new wallpaper** from another device or web admin panel
3. **Check if notification is received** on the closed app device
4. **Open the app** and verify the new wallpaper is visible

## Monitoring and Debugging

### Key Log Messages to Watch For

**Successful OAuth Token Generation**:
```
🔑 Getting OAuth access token...
✅ OAuth access token obtained successfully
```

**Successful Wallpaper Upload**:
```
💾 Saving wallpaper to Firebase: [id]
✅ Wallpaper saved to Firebase successfully
✅ New wallpaper verified in list: [title]
```

**Successful Notification Sending**:
```
✅ FCM HTTP v1 request successful!
📱 Notification sent to topic: all_users
```

### Error Messages to Watch For

**OAuth Token Errors**:
```
❌ Error getting OAuth access token: FormatException
💡 Check if service account key format is correct
```

**Wallpaper Loading Errors**:
```
❌ Wallpaper not found in list: [title]
❌ Error parsing wallpaper [id]: [error]
```

**FCM Request Errors**:
```
❌ FCM HTTP v1 request failed: [status_code]
📋 Dio error details: [error_details]
```

## Additional Recommendations

### 1. Firebase Indexes

Create the missing Firestore index for `live_notifications`:
1. Go to Firebase Console > Firestore > Indexes
2. Create a composite index for:
   - Collection: `live_notifications`
   - Fields: `timestamp` (Descending), `read` (Ascending)

### 2. App Store Optimization

For better background notification delivery:
- Request battery optimization exemption
- Guide users to disable battery optimization for the app
- Implement notification channels properly

### 3. Monitoring

Set up Firebase Analytics to track:
- Notification delivery rates
- Wallpaper upload success rates
- User engagement with new wallpapers

## Rollback Plan

If issues occur, you can rollback by:
1. Reverting the changes in `admin_notification_service.dart`
2. Reverting the changes in `wallpaper_provider.dart`
3. Using the original service account key format

## Next Steps

1. **Test thoroughly** in development environment
2. **Deploy to staging** for further testing
3. **Monitor logs** for any new issues
4. **Gather user feedback** on notification delivery
5. **Optimize performance** based on usage patterns