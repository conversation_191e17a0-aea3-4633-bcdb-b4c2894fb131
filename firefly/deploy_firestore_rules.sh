#!/bin/bash

# Deploy Firestore Security Rules
# Make sure you have Firebase CLI installed: npm install -g firebase-tools
# Make sure you're logged in: firebase login

echo "🔥 Deploying Firestore Security Rules..."

# Initialize Firebase project if not already done
if [ ! -f "firebase.json" ]; then
    echo "⚠️  firebase.json not found. Please run 'firebase init firestore' first."
    echo "📝 When prompted, select your Firebase project and use 'firestore.rules' as the rules file."
    exit 1
fi

# Deploy the rules
firebase deploy --only firestore:rules

if [ $? -eq 0 ]; then
    echo "✅ Firestore rules deployed successfully!"
    echo "📱 FCM tokens can now be saved to Firestore."
else
    echo "❌ Failed to deploy Firestore rules."
    echo "🔧 Please check your Firebase CLI setup and project configuration."
fi