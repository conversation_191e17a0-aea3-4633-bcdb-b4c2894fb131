name: firefly
description: "FireFly - 4K HD Wallpaper App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+13

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Material Design
  cupertino_icons: ^1.0.8
  
  # Firebase
  firebase_core: ^2.32.0
  firebase_auth: ^4.20.0
  firebase_database: ^10.5.7
  cloud_firestore: ^4.17.4
  firebase_messaging: ^14.9.4
  firebase_analytics: ^10.10.7
  flutter_local_notifications: ^17.2.2
  google_sign_in: ^6.2.1
  
  # Image handling
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4
  cloudinary_public: ^0.21.0
  gal: ^2.3.0
  
  # Wallpaper management
  # async_wallpaper: ^2.1.0  # Has namespace issues with current Android Gradle Plugin
  wallpaper_manager_flutter: ^1.0.1
  
  # Storage and permissions
  permission_handler: ^11.1.0
  path_provider: ^2.1.1
  device_info_plus: ^9.1.0
  
  # HTTP and networking
  http: ^0.13.6
  dio: ^5.3.2
  googleapis_auth: ^1.4.1
  crypto: ^3.0.3
  
  # UI utilities
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0
  photo_view: ^0.14.0
  flutter_svg: ^2.0.9
  
  # State management
  provider: ^6.1.1
  
  # URL launcher for external links
  url_launcher: ^6.2.1
  
  # Package info for version checking
  package_info_plus: ^4.2.0

  # Local storage
  shared_preferences: ^2.2.2
  


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
fonts:
  - family: Poppins
    fonts:
      - asset: assets/fonts/Poppins-Regular.ttf
      - asset: assets/fonts/Poppins-Bold.ttf
        weight: 700
