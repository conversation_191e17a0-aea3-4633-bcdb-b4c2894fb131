<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Elegant gradient for the firefly -->    
    <linearGradient id="fireflyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#5B73FF;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF1744;stop-opacity:1" />
    </linearGradient>
    
    <!-- Soft glow gradient -->
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="60%">
      <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#5B73FF;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#9C27B0;stop-opacity:0" />
    </radialGradient>
    
    <!-- Wing gradient -->
    <linearGradient id="wingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4FF;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#5B73FF;stop-opacity:0.3" />
    </linearGradient>
    
    <!-- Tail light gradient -->
    <radialGradient id="tailGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00FFFF;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#00D4FF;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#5B73FF;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background glow -->
  <circle cx="100" cy="100" r="85" fill="url(#glowGradient)" opacity="0.3"/>
  
  <!-- Main firefly design -->
  <g transform="translate(100,100)">
    
    <!-- Firefly body segments -->
    <!-- Head -->
    <ellipse cx="0" cy="-25" rx="8" ry="12" fill="#1A1A1A" stroke="url(#fireflyGradient)" stroke-width="1" opacity="0.9"/>
    
    <!-- Thorax -->
    <ellipse cx="0" cy="-8" rx="12" ry="15" fill="#2A2A2A" stroke="url(#fireflyGradient)" stroke-width="1.5" opacity="0.9"/>
    
    <!-- Abdomen -->
    <ellipse cx="0" cy="15" rx="10" ry="20" fill="#1A1A1A" stroke="url(#fireflyGradient)" stroke-width="1" opacity="0.9"/>
    
    <!-- Elegant wings -->
    <!-- Primary wings -->
    <path d="M -15 -15 Q -35 -25 -45 -10 Q -35 5 -15 -5 Z" fill="url(#wingGradient)" opacity="0.7"/>
    <path d="M 15 -15 Q 35 -25 45 -10 Q 35 5 15 -5 Z" fill="url(#wingGradient)" opacity="0.7"/>
    
    <!-- Secondary wings -->
    <path d="M -12 -8 Q -25 -15 -30 -5 Q -25 5 -12 0 Z" fill="url(#wingGradient)" opacity="0.5"/>
    <path d="M 12 -8 Q 25 -15 30 -5 Q 25 5 12 0 Z" fill="url(#wingGradient)" opacity="0.5"/>
    
    <!-- Glowing tail light -->
    <circle cx="0" cy="30" r="6" fill="url(#tailGradient)" opacity="0.9"/>
    <circle cx="0" cy="30" r="3" fill="#00FFFF" opacity="1"/>
    <circle cx="0" cy="30" r="1" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- Antennae -->
    <line x1="-3" y1="-35" x2="-8" y2="-45" stroke="url(#fireflyGradient)" stroke-width="1" opacity="0.8"/>
    <line x1="3" y1="-35" x2="8" y2="-45" stroke="url(#fireflyGradient)" stroke-width="1" opacity="0.8"/>
    <circle cx="-8" cy="-45" r="1.5" fill="#00D4FF" opacity="0.9"/>
    <circle cx="8" cy="-45" r="1.5" fill="#00D4FF" opacity="0.9"/>
    
    <!-- Legs -->
    <line x1="-8" y1="-5" x2="-15" y2="5" stroke="#5B73FF" stroke-width="1" opacity="0.6"/>
    <line x1="8" y1="-5" x2="15" y2="5" stroke="#5B73FF" stroke-width="1" opacity="0.6"/>
    <line x1="-6" y1="8" x2="-12" y2="18" stroke="#5B73FF" stroke-width="1" opacity="0.6"/>
    <line x1="6" y1="8" x2="12" y2="18" stroke="#5B73FF" stroke-width="1" opacity="0.6"/>
    
  </g>
  
  <!-- Floating light particles -->
  <circle cx="40" cy="40" r="2" fill="#00FFFF" opacity="0.8">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="160" cy="50" r="1.5" fill="#5B73FF" opacity="0.6">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="50" cy="160" r="1" fill="#9C27B0" opacity="0.7">
    <animate attributeName="opacity" values="0.4;0.7;0.4" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="150" cy="150" r="2.5" fill="#00D4FF" opacity="0.5">
    <animate attributeName="opacity" values="0.2;0.5;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Subtle outer ring -->
  <circle cx="100" cy="100" r="95" fill="none" stroke="url(#fireflyGradient)" stroke-width="0.5" opacity="0.3"/>
  
</svg>