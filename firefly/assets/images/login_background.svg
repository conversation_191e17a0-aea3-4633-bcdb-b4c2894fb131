<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Background gradient -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0a0a0a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
    </linearGradient>
    
    <!-- Firefly glow gradient -->
    <radialGradient id="fireflyGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.8" />
      <stop offset="30%" style="stop-color:#7b68ee;stop-opacity:0.6" />
      <stop offset="70%" style="stop-color:#9932cc;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#4b0082;stop-opacity:0" />
    </radialGradient>
    
    <!-- Star glow -->
    <radialGradient id="starGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="50%" style="stop-color:#00d4ff;stop-opacity:0.5" />
      <stop offset="100%" style="stop-color:#transparent;stop-opacity:0" />
    </radialGradient>
    
    <!-- Particle glow -->
    <radialGradient id="particleGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#7b68ee;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#transparent;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#bgGradient)"/>
  
  <!-- Stars in background -->
  <circle cx="50" cy="40" r="1" fill="url(#starGlow)" opacity="0.7">
    <animate attributeName="opacity" values="0.3;0.9;0.3" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="120" cy="25" r="0.8" fill="url(#starGlow)" opacity="0.5">
    <animate attributeName="opacity" values="0.2;0.8;0.2" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="35" r="1.2" fill="url(#starGlow)" opacity="0.6">
    <animate attributeName="opacity" values="0.4;1;0.4" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="50" r="0.9" fill="url(#starGlow)" opacity="0.4">
    <animate attributeName="opacity" values="0.1;0.7;0.1" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="370" cy="30" r="0.7" fill="url(#starGlow)" opacity="0.5">
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="2.8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Main firefly -->
  <g transform="translate(200,150)">
    <!-- Firefly glow effect -->
    <circle cx="0" cy="0" r="25" fill="url(#fireflyGlow)" opacity="0.6">
      <animate attributeName="r" values="20;30;20" dur="2s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Firefly body -->
    <ellipse cx="0" cy="0" rx="8" ry="12" fill="#2c2c54" stroke="#00d4ff" stroke-width="0.5"/>
    
    <!-- Firefly light -->
    <circle cx="0" cy="3" r="4" fill="#00d4ff" opacity="0.9">
      <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Wings -->
    <ellipse cx="-6" cy="-8" rx="4" ry="8" fill="#ffffff" opacity="0.3" transform="rotate(-20)"/>
    <ellipse cx="6" cy="-8" rx="4" ry="8" fill="#ffffff" opacity="0.3" transform="rotate(20)"/>
    
    <!-- Antennae -->
    <line x1="-2" y1="-12" x2="-4" y2="-16" stroke="#7b68ee" stroke-width="0.5"/>
    <line x1="2" y1="-12" x2="4" y2="-16" stroke="#7b68ee" stroke-width="0.5"/>
    <circle cx="-4" cy="-16" r="0.8" fill="#00d4ff"/>
    <circle cx="4" cy="-16" r="0.8" fill="#00d4ff"/>
  </g>
  
  <!-- Floating particles -->
  <circle cx="80" cy="120" r="2" fill="url(#particleGlow)" opacity="0.6">
    <animate attributeName="cy" values="120;100;120" dur="4s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.3;0.8;0.3" dur="4s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="300" cy="180" r="1.5" fill="url(#particleGlow)" opacity="0.5">
    <animate attributeName="cy" values="180;160;180" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.7;0.2" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="150" cy="220" r="1.8" fill="url(#particleGlow)" opacity="0.7">
    <animate attributeName="cy" values="220;200;220" dur="3.5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.4;0.9;0.4" dur="3.5s" repeatCount="indefinite"/>
  </circle>
  
  <circle cx="350" cy="120" r="1.2" fill="url(#particleGlow)" opacity="0.4">
    <animate attributeName="cy" values="120;105;120" dur="2.5s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Light trails -->
  <path d="M 50 250 Q 100 200 150 250 Q 200 200 250 250 Q 300 200 350 250" 
        stroke="url(#particleGlow)" stroke-width="1" fill="none" opacity="0.3">
    <animate attributeName="opacity" values="0.1;0.5;0.1" dur="5s" repeatCount="indefinite"/>
  </path>
  
  <!-- Additional small fireflies -->
  <g transform="translate(100,80)">
    <circle cx="0" cy="0" r="8" fill="url(#fireflyGlow)" opacity="0.4">
      <animate attributeName="opacity" values="0.2;0.6;0.2" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="2" fill="#7b68ee" opacity="0.8">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <g transform="translate(320,200)">
    <circle cx="0" cy="0" r="6" fill="url(#fireflyGlow)" opacity="0.3">
      <animate attributeName="opacity" values="0.1;0.5;0.1" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="0" r="1.5" fill="#9932cc" opacity="0.7">
      <animate attributeName="opacity" values="0.4;0.9;0.4" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>
</svg>