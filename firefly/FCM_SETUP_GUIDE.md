# Firebase Cloud Messaging (FCM) Setup Guide - HTTP v1 API (OAuth 2.0)

## 🔥 Important Update (June 2024)
Firebase ने Legacy Server Key system को deprecate कर दिया है। अब **OAuth 2.0 based HTTP v1 API** का use करना mandatory है।

## Problem
Notifications are not being received on devices where the app is installed but not actively open. Legacy FCM API is deprecated and no longer works.

## Solution
We've implemented FCM HTTP v1 API with OAuth 2.0 authentication to send push notifications directly to devices, even when the app is closed.

## Setup Instructions

### Step 1: Generate Service Account Key

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Click on **Project Settings** (gear icon)
4. Go to **Service Accounts** tab
5. Click **Generate New Private Key**
6. Download the JSON file
7. Copy the entire JSON content

### Step 2: Configure the Service Account Key

1. Open `lib/config/firebase_config.dart`
2. Replace the `serviceAccountKeyJson` with your actual service account JSON:

```dart
static const String serviceAccountKeyJson = '''
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
''';
```

### Step 3: Install Dependencies

Run the following command to install the new OAuth dependency:

```bash
flutter pub get
```

### Step 4: Test Notifications

1. Build and install the app on a test device
2. Close the app completely (not just minimize)
3. Go to Admin Panel and send a notification
4. The notification should appear on the device even when the app is closed

## How It Works

### Before Fix (Legacy API)
- Used deprecated server key authentication
- Firebase stopped supporting this method
- Notifications failed to send

### After Fix (HTTP v1 API)
- Uses OAuth 2.0 service account authentication
- Generates access tokens dynamically
- Converts legacy message format to v1 format
- Works with current Firebase infrastructure

## Technical Details

### Files Modified

1. **`pubspec.yaml`**
   - Added `googleapis_auth: ^1.4.1` dependency

2. **`lib/config/firebase_config.dart`**
   - Replaced legacy server key with service account JSON
   - Added FCM OAuth scopes

3. **`lib/services/admin_notification_service.dart`**
   - Added OAuth 2.0 authentication
   - Implemented message format conversion
   - Updated HTTP v1 API endpoint

### New FCM Message Flow

1. Admin sends notification from admin panel
2. `AdminNotificationService.sendNotification()` is called
3. Service account credentials are used to get OAuth token
4. Legacy message format is converted to HTTP v1 format
5. HTTP v1 API call is made with Bearer token
6. FCM delivers notification to all subscribed devices
7. Background handler processes notification on device
8. Local notification is shown to user

### OAuth 2.0 Authentication Flow

1. **Service Account**: Uses private key for authentication
2. **Access Token**: Generated dynamically for each request
3. **Scopes**: `https://www.googleapis.com/auth/firebase.messaging`
4. **Bearer Token**: Used in Authorization header

### Message Format Conversion

**Legacy Format** → **HTTP v1 Format**
- `to: "/topics/all_users"` → `topic: "all_users"`
- Notification payload remains similar
- Android/APNS specific settings are preserved
- Data payload is converted to string map

## Troubleshooting

### Notifications Still Not Working?

1. **Check Service Account**: Ensure JSON is valid and complete
2. **Check Project ID**: Verify project ID matches your Firebase project
3. **Check Permissions**: Service account should have FCM permissions
4. **Check Console Logs**: Look for OAuth and FCM success/error messages
5. **Test Topic Subscription**: Verify devices are subscribed to 'all_users' topic

### Common Issues

- **401 Unauthorized**: Invalid service account or expired token
- **403 Forbidden**: Service account lacks FCM permissions
- **400 Bad Request**: Malformed HTTP v1 message format
- **Device not receiving**: Check topic subscription and permissions

### Debug Commands

Check logs for these messages:
- `✅ FCM HTTP v1 request successful`
- `❌ FCM HTTP v1 request failed`
- `⚠️ Service Account Key not configured`
- `❌ Failed to get OAuth access token`

## Security Notes

⚠️ **Critical Security Warning**: 
- Never commit your service account JSON to version control
- Use environment variables or secure configuration management
- Rotate service account keys regularly
- Limit service account permissions to FCM only

## Migration from Legacy API

### What Changed:
- ❌ **Legacy**: `Authorization: key=SERVER_KEY`
- ✅ **New**: `Authorization: Bearer OAUTH_TOKEN`
- ❌ **Legacy**: `https://fcm.googleapis.com/fcm/send`
- ✅ **New**: `https://fcm.googleapis.com/v1/projects/PROJECT_ID/messages:send`

### Benefits of HTTP v1 API:
- ✅ Future-proof (won't be deprecated)
- ✅ Better security with OAuth 2.0
- ✅ More granular permissions
- ✅ Better error handling
- ✅ Support for latest FCM features

## Testing Checklist

- [ ] Service account JSON configured in `firebase_config.dart`
- [ ] `flutter pub get` executed successfully
- [ ] App installed on test device
- [ ] App completely closed (not just minimized)
- [ ] Notification sent from admin panel
- [ ] Notification received on device
- [ ] Background handler logs show successful processing
- [ ] OAuth token generation working
- [ ] HTTP v1 API calls successful

Once setup is complete, notifications will be delivered to all devices with the app installed using the modern, secure FCM HTTP v1 API! 🚀