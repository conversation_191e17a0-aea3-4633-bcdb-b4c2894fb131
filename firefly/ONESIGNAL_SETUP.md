# OneSignal Push Notifications Setup Guide

This guide will help you set up OneSignal push notifications for your FireFly wallpaper app.

## 🚀 Quick Setup

### 1. Create OneSignal Account
1. Go to [OneSignal.com](https://onesignal.com) and create a free account
2. Create a new app in the OneSignal dashboard
3. Configure platforms (Android & iOS)

### 2. Get Your App ID
1. In OneSignal dashboard, go to **Settings > Keys & IDs**
2. Copy your **OneSignal App ID**
3. Update the App ID in your code:

```dart
// In lib/config/onesignal_config.dart
static const String appId = 'YOUR_ACTUAL_ONESIGNAL_APP_ID';
```

### 3. Android Configuration
1. In OneSignal dashboard, add Android platform
2. Upload your Firebase Server Key (from Firebase Console > Project Settings > Cloud Messaging)
3. Update Android manifest (already done in the code):

```xml
<!-- In android/app/src/main/AndroidManifest.xml -->
<meta-data
    android:name="onesignal_app_id"
    android:value="YOUR_ACTUAL_ONESIGNAL_APP_ID" />
```

### 4. iOS Configuration (if supporting iOS)
1. In OneSignal dashboard, add iOS platform
2. Upload your Apple Push Certificate (.p12) or Auth Key (.p8)
3. Follow the iOS setup steps in the OneSignal documentation

## 📱 Features Included

### ✅ **Push Notifications**
- New wallpaper alerts
- Category updates
- App updates
- Custom messaging

### ✅ **User Segmentation**
- Platform-based (Android/iOS)
- User type (anonymous/authenticated)
- App version tracking
- Language preferences
- Custom tags

### ✅ **Analytics Integration**
- Notification received tracking
- Click-through tracking
- User engagement metrics

### ✅ **Deep Linking**
- Navigate to specific wallpapers
- Open specific categories
- Custom app actions

## 🔧 Configuration Options

### User Tags
The app automatically sets these tags for better targeting:
- `platform`: android/ios
- `app_version`: Current app version
- `user_type`: anonymous/authenticated
- `language`: User's device language
- `notifications_enabled`: true/false

### Notification Types
- `new_wallpaper`: New wallpaper added
- `category_update`: Category updated with new content
- `app_update`: App version update available
- `general`: General announcements

## 📝 Usage Examples

### Send Test Notification
```bash
curl -X POST https://api.onesignal.com/notifications \
  -H "Content-Type: application/json; charset=utf-8" \
  -H "Authorization: Key YOUR_REST_API_KEY" \
  -d '{
    "app_id": "YOUR_APP_ID",
    "target_channel": "push",
    "headings": {"en": "🔥 New Wallpaper!"},
    "contents": {"en": "Check out the latest Nature wallpaper"},
    "included_segments": ["All"],
    "data": {
      "type": "new_wallpaper",
      "wallpaper_id": "12345",
      "category": "Nature"
    }
  }'
```

### Send to Specific Users
```bash
# Target Android users only
"filters": [
  {"field": "tag", "key": "platform", "relation": "=", "value": "android"}
]

# Target authenticated users
"filters": [
  {"field": "tag", "key": "user_type", "relation": "=", "value": "authenticated"}
]
```

## 🛠️ Advanced Features

### Custom User Properties
```dart
// Add custom tags
await OneSignalService.sendTag('favorite_category', 'Nature');
await OneSignalService.sendTag('premium_user', 'true');

// Remove tags
await OneSignalService.removeTag('old_preference');
```

### External User ID
```dart
// Link OneSignal user with your backend user ID
OneSignal.login('your_backend_user_id');
```

## 🔍 Testing

### 1. Test Subscriptions
1. Install app on test device
2. Allow push notifications
3. In OneSignal dashboard: **Audience > Subscriptions**
4. Find your device and add to "Test Subscriptions"

### 2. Send Test Message
1. Go to **Messages > Push > New Push**
2. Target "Test Subscriptions" segment
3. Send test message

### 3. Verify Integration
- Check device receives notification
- Verify notification click opens app
- Check analytics in OneSignal dashboard

## 📊 Analytics & Monitoring

### OneSignal Dashboard
- **Delivery**: Message delivery stats
- **Audience**: User segments and subscriptions
- **Messages**: Campaign performance

### Firebase Analytics Integration
- Notification received events
- Click-through tracking
- User engagement metrics

## 🚨 Troubleshooting

### Common Issues
1. **No notifications received**
   - Check App ID is correct
   - Verify Firebase configuration
   - Ensure device has internet connection

2. **iOS notifications not working**
   - Check Apple Push Certificate
   - Verify bundle ID matches
   - Test on physical device (not simulator)

3. **Android notifications not showing**
   - Check Firebase Server Key
   - Verify Google Play Services installed
   - Check notification permissions

### Debug Mode
Enable verbose logging for debugging:
```dart
OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
```

## 📚 Resources

- [OneSignal Documentation](https://documentation.onesignal.com/)
- [Flutter SDK Reference](https://documentation.onesignal.com/docs/flutter-sdk-setup)
- [REST API Reference](https://documentation.onesignal.com/reference)

## 🎯 Next Steps

1. **Replace placeholder App ID** with your actual OneSignal App ID
2. **Test notifications** on real devices
3. **Create user segments** for targeted messaging
4. **Set up automated campaigns** for new wallpapers
5. **Monitor analytics** and optimize engagement

---

**Need Help?** Contact OneSignal support or check their comprehensive documentation.
