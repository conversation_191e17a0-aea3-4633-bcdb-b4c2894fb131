import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:firefly/services/app_rating_service.dart';
import 'package:firefly/services/rating_prompt_service.dart';
import 'package:firefly/widgets/app_rating_dialog.dart';
import 'package:firefly/widgets/app_sharing_widget.dart';

void main() {
  group('Rating System Tests', () {
    test('App Rating Service - Engagement Stats', () async {
      // Test getting engagement stats
      final stats = await AppRatingService.getEngagementStats();
      
      expect(stats, isA<Map<String, int>>());
      expect(stats.containsKey('launches'), true);
      expect(stats.containsKey('downloads'), true);
    });

    test('App Rating Service - Should Show Rating Prompt', () async {
      // Reset preferences first
      await AppRatingService.resetRatingPreferences();
      
      // Initially should not show (no engagement)
      bool shouldShow = await AppRatingService.shouldShowRatingPrompt();
      expect(shouldShow, false);
      
      // After incrementing launches, should show
      for (int i = 0; i < 6; i++) {
        await AppRatingService.incrementAppLaunchCount();
      }
      
      shouldShow = await AppRatingService.shouldShowRatingPrompt();
      expect(shouldShow, true);
    });

    test('App Rating Service - Download Tracking', () async {
      await AppRatingService.resetRatingPreferences();
      
      final initialStats = await AppRatingService.getEngagementStats();
      final initialDownloads = initialStats['downloads'] ?? 0;
      
      // Increment download count
      await AppRatingService.incrementDownloadCount();
      
      final updatedStats = await AppRatingService.getEngagementStats();
      final updatedDownloads = updatedStats['downloads'] ?? 0;
      
      expect(updatedDownloads, initialDownloads + 1);
    });

    test('Rating Prompt Service - Engagement Stats', () async {
      final stats = await RatingPromptService.getEngagementStats();
      
      expect(stats, isA<Map<String, dynamic>>());
      expect(stats.containsKey('sessions'), true);
      expect(stats.containsKey('wallpaper_views'), true);
      expect(stats.containsKey('downloads_session'), true);
      expect(stats.containsKey('total_launches'), true);
      expect(stats.containsKey('total_downloads'), true);
      expect(stats.containsKey('eligible_for_prompt'), true);
    });

    testWidgets('App Rating Dialog - Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppRatingDialog(
              onRated: () {},
              onDismissed: () {},
            ),
          ),
        ),
      );

      // Verify dialog elements are present
      expect(find.text('Enjoying FireFly?'), findsOneWidget);
      expect(find.text('Rate on Play Store'), findsOneWidget);
      expect(find.text('Maybe Later'), findsOneWidget);
      expect(find.text('Don\'t ask again'), findsOneWidget);
      expect(find.byIcon(Icons.star), findsWidgets);
    });

    testWidgets('App Sharing Widget - Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppSharingWidget(
              onShared: () {},
            ),
          ),
        ),
      );

      // Verify sharing widget elements are present
      expect(find.text('Share FireFly'), findsOneWidget);
      expect(find.text('Share App'), findsOneWidget);
      expect(find.text('Rate & Share'), findsOneWidget);
      expect(find.text('Copy Link'), findsOneWidget);
      expect(find.byIcon(Icons.share), findsOneWidget);
      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.byIcon(Icons.link), findsOneWidget);
    });

    testWidgets('Wallpaper Sharing Widget - Widget Test', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WallpaperSharingWidget(
              wallpaperTitle: 'Test Wallpaper',
              wallpaperUrl: 'https://example.com/wallpaper.jpg',
              onShared: () {},
            ),
          ),
        ),
      );

      // Verify wallpaper sharing widget elements are present
      expect(find.text('Share Wallpaper'), findsOneWidget);
      expect(find.text('Share "Test Wallpaper" with friends!'), findsOneWidget);
      expect(find.text('Share Wallpaper'), findsWidgets);
      expect(find.byIcon(Icons.share), findsOneWidget);
    });

    test('Rating Prompt Service - Reset Engagement Tracking', () async {
      // Add some engagement
      await AppRatingService.incrementAppLaunchCount();
      await AppRatingService.incrementDownloadCount();
      await RatingPromptService.trackWallpaperView();
      
      // Reset tracking
      await RatingPromptService.resetEngagementTracking();
      
      // Verify stats are reset
      final stats = await RatingPromptService.getEngagementStats();
      expect(stats['sessions'], 0);
      expect(stats['wallpaper_views'], 0);
      expect(stats['downloads_session'], 0);
      expect(stats['total_launches'], 0);
      expect(stats['total_downloads'], 0);
    });

    test('App Rating Service - Never Show Rating', () async {
      await AppRatingService.resetRatingPreferences();
      
      // Should initially be eligible
      bool shouldShow = await AppRatingService.shouldShowRatingPrompt();
      
      // Mark never show
      await AppRatingService.markNeverShowRating();
      
      // Should no longer be eligible
      shouldShow = await AppRatingService.shouldShowRatingPrompt();
      expect(shouldShow, false);
    });
  });

  group('Integration Tests', () {
    test('Complete Rating Flow Simulation', () async {
      // Reset everything
      await AppRatingService.resetRatingPreferences();
      await RatingPromptService.resetEngagementTracking();
      
      // Simulate user engagement
      for (int i = 0; i < 3; i++) {
        await AppRatingService.incrementAppLaunchCount();
      }
      
      for (int i = 0; i < 5; i++) {
        await RatingPromptService.trackWallpaperView();
      }
      
      await AppRatingService.incrementDownloadCount();
      await AppRatingService.incrementDownloadCount();
      
      // Check if eligible for rating prompt
      final shouldShow = await AppRatingService.shouldShowRatingPrompt();
      expect(shouldShow, true);
      
      // Get engagement stats
      final stats = await RatingPromptService.getEngagementStats();
      expect(stats['total_launches'], 3);
      expect(stats['wallpaper_views'], 5);
      expect(stats['total_downloads'], 2);
      expect(stats['eligible_for_prompt'], true);
    });
  });
}
