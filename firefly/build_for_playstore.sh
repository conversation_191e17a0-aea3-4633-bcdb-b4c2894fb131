#!/bin/bash

# FireFly App - Play Store Build Script
# This script builds the app bundle ready for Play Store submission

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# App information
APP_NAME="FireFly"
PACKAGE_NAME="com.anuved.Firefly"
BUILD_DIR="build/app/outputs"
AAB_PATH="$BUILD_DIR/bundle/release/app-release.aab"
APK_PATH="$BUILD_DIR/flutter-apk/app-release.apk"

# Get current version from pubspec.yaml
get_version() {
    if [ -f "pubspec.yaml" ]; then
        VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: //')
        echo "$VERSION"
    else
        echo "unknown"
    fi
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if Flutter is installed
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter is not installed or not in PATH"
        exit 1
    fi
    print_success "Flutter found: $(flutter --version | head -n1)"
    
    # Check if we're in the right directory
    if [ ! -f "pubspec.yaml" ]; then
        print_error "pubspec.yaml not found. Are you in the Flutter project root?"
        exit 1
    fi
    print_success "Flutter project detected"
    
    # Check if keystore configuration exists
    if [ ! -f "android/key.properties" ]; then
        print_error "android/key.properties not found!"
        echo "Please run ./create_keystore.sh first and configure signing"
        exit 1
    fi
    print_success "Keystore configuration found"
    
    # Check if keystore file exists
    KEYSTORE_PATH=$(grep 'storeFile=' android/key.properties | cut -d'=' -f2)
    if [ ! -f "$KEYSTORE_PATH" ]; then
        print_error "Keystore file not found: $KEYSTORE_PATH"
        exit 1
    fi
    print_success "Keystore file found: $KEYSTORE_PATH"
    
    echo ""
}

# Clean project
clean_project() {
    print_header "Cleaning Project"
    
    print_status "Running flutter clean..."
    flutter clean
    
    print_status "Removing build directory..."
    rm -rf build/
    
    print_success "Project cleaned successfully"
    echo ""
}

# Get dependencies
get_dependencies() {
    print_header "Getting Dependencies"
    
    print_status "Running flutter pub get..."
    flutter pub get
    
    print_success "Dependencies updated successfully"
    echo ""
}

# Run tests
run_tests() {
    print_header "Running Tests"
    
    if [ -d "test" ] && [ "$(ls -A test)" ]; then
        print_status "Running unit tests..."
        flutter test
        print_success "All tests passed"
    else
        print_warning "No tests found, skipping..."
    fi
    echo ""
}

# Analyze code
analyze_code() {
    print_header "Analyzing Code"
    
    print_status "Running flutter analyze..."
    flutter analyze
    
    if [ $? -eq 0 ]; then
        print_success "Code analysis passed"
    else
        print_warning "Code analysis found issues, but continuing..."
    fi
    echo ""
}

# Build app bundle
build_app_bundle() {
    print_header "Building App Bundle"
    
    print_status "Building release app bundle..."
    flutter build appbundle --release --verbose
    
    if [ $? -eq 0 ]; then
        print_success "App bundle built successfully!"
        
        # Check if file exists and get size
        if [ -f "$AAB_PATH" ]; then
            FILE_SIZE=$(du -h "$AAB_PATH" | cut -f1)
            print_success "App bundle size: $FILE_SIZE"
            print_success "Location: $AAB_PATH"
        else
            print_error "App bundle file not found at expected location"
            exit 1
        fi
    else
        print_error "Failed to build app bundle"
        exit 1
    fi
    echo ""
}

# Build APK (for testing)
build_apk() {
    print_header "Building APK (for testing)"
    
    print_status "Building release APK..."
    flutter build apk --release
    
    if [ $? -eq 0 ]; then
        print_success "APK built successfully!"
        
        # Check if file exists and get size
        if [ -f "$APK_PATH" ]; then
            FILE_SIZE=$(du -h "$APK_PATH" | cut -f1)
            print_success "APK size: $FILE_SIZE"
            print_success "Location: $APK_PATH"
        else
            print_error "APK file not found at expected location"
        fi
    else
        print_error "Failed to build APK"
    fi
    echo ""
}

# Verify app bundle
verify_bundle() {
    print_header "Verifying App Bundle"
    
    if [ -f "$AAB_PATH" ]; then
        print_status "Checking app bundle contents..."
        
        # Use bundletool if available
        if command -v bundletool &> /dev/null; then
            print_status "Validating with bundletool..."
            bundletool validate --bundle="$AAB_PATH"
            
            if [ $? -eq 0 ]; then
                print_success "App bundle validation passed"
            else
                print_warning "App bundle validation failed"
            fi
        else
            print_warning "bundletool not found, skipping validation"
            print_status "Install bundletool for advanced validation:"
            print_status "https://github.com/google/bundletool"
        fi
        
        # Basic file checks
        print_status "Basic file verification..."
        file "$AAB_PATH"
        print_success "App bundle file is valid"
    else
        print_error "App bundle not found for verification"
        exit 1
    fi
    echo ""
}

# Generate build report
generate_report() {
    print_header "Build Report"
    
    VERSION=$(get_version)
    BUILD_DATE=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "App Name: $APP_NAME"
    echo "Package: $PACKAGE_NAME"
    echo "Version: $VERSION"
    echo "Build Date: $BUILD_DATE"
    echo "Flutter Version: $(flutter --version | head -n1)"
    echo ""
    
    echo "Build Outputs:"
    if [ -f "$AAB_PATH" ]; then
        AAB_SIZE=$(du -h "$AAB_PATH" | cut -f1)
        echo "  App Bundle: $AAB_PATH ($AAB_SIZE)"
    fi
    
    if [ -f "$APK_PATH" ]; then
        APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
        echo "  APK: $APK_PATH ($APK_SIZE)"
    fi
    echo ""
    
    # Save report to file
    REPORT_FILE="build_report_$(date '+%Y%m%d_%H%M%S').txt"
    {
        echo "FireFly App Build Report"
        echo "========================"
        echo "App Name: $APP_NAME"
        echo "Package: $PACKAGE_NAME"
        echo "Version: $VERSION"
        echo "Build Date: $BUILD_DATE"
        echo "Flutter Version: $(flutter --version | head -n1)"
        echo ""
        echo "Build Outputs:"
        if [ -f "$AAB_PATH" ]; then
            echo "  App Bundle: $AAB_PATH ($AAB_SIZE)"
        fi
        if [ -f "$APK_PATH" ]; then
            echo "  APK: $APK_PATH ($APK_SIZE)"
        fi
        echo ""
        echo "Build completed successfully at $BUILD_DATE"
    } > "$REPORT_FILE"
    
    print_success "Build report saved: $REPORT_FILE"
    echo ""
}

# Show next steps
show_next_steps() {
    print_header "Next Steps"
    
    echo "🎉 Build completed successfully!"
    echo ""
    echo "📋 Next Steps for Play Store Submission:"
    echo "1. Test the app bundle thoroughly"
    echo "2. Prepare store listing materials:"
    echo "   - Screenshots (phone & tablet)"
    echo "   - Feature graphic (1024x500)"
    echo "   - App description"
    echo "   - Privacy policy"
    echo "3. Upload to Google Play Console"
    echo "4. Complete store listing"
    echo "5. Submit for review"
    echo ""
    echo "📁 Build Files:"
    if [ -f "$AAB_PATH" ]; then
        echo "   App Bundle: $AAB_PATH"
    fi
    if [ -f "$APK_PATH" ]; then
        echo "   APK (testing): $APK_PATH"
    fi
    echo ""
    echo "📚 Documentation:"
    echo "   - PLAY_STORE_LISTING.md"
    echo "   - PLAY_STORE_SUBMISSION_GUIDE.md"
    echo "   - RELEASE_NOTES.md"
    echo ""
    
    # Ask if user wants to open build directory
    read -p "Open build directory? (y/n): " open_dir
    if [ "$open_dir" = "y" ] || [ "$open_dir" = "Y" ]; then
        if command -v open &> /dev/null; then
            open "$BUILD_DIR"
        elif command -v xdg-open &> /dev/null; then
            xdg-open "$BUILD_DIR"
        else
            print_status "Build directory: $BUILD_DIR"
        fi
    fi
}

# Main function
main() {
    print_header "FireFly App - Play Store Build"
    echo "Starting build process for Play Store submission..."
    echo ""
    
    # Run all build steps
    check_prerequisites
    clean_project
    get_dependencies
    analyze_code
    run_tests
    build_app_bundle
    build_apk
    verify_bundle
    generate_report
    show_next_steps
    
    print_success "🚀 Build process completed successfully!"
}

# Handle script arguments
case "${1:-}" in
    "--help" | "-h")
        echo "FireFly App - Play Store Build Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --bundle-only  Build only app bundle (skip APK)"
        echo "  --apk-only     Build only APK (skip app bundle)"
        echo "  --no-tests     Skip running tests"
        echo "  --no-analyze   Skip code analysis"
        echo ""
        echo "Examples:"
        echo "  $0                 # Full build process"
        echo "  $0 --bundle-only  # Build only app bundle"
        echo "  $0 --no-tests     # Skip tests"
        exit 0
        ;;
    "--bundle-only")
        check_prerequisites
        clean_project
        get_dependencies
        analyze_code
        run_tests
        build_app_bundle
        verify_bundle
        generate_report
        show_next_steps
        ;;
    "--apk-only")
        check_prerequisites
        clean_project
        get_dependencies
        analyze_code
        run_tests
        build_apk
        generate_report
        show_next_steps
        ;;
    *)
        main
        ;;
esac