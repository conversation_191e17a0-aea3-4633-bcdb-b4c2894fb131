import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/notification_service.dart';

class NotificationPermissionDialog {
  static Future<void> showPermissionDialog(BuildContext context) async {
    // Check if permission is already granted
    bool hasPermission = await NotificationService.hasNotificationPermission();
    if (hasPermission) {
      return;
    }

    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.notifications_active, color: Colors.orange),
              SizedBox(width: 8),
              Text('Enable Notifications'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Get notified about new wallpapers and updates!',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              if (Platform.isAndroid) ...[
                const Text(
                  '• New wallpaper collections\n'
                  '• Featured wallpapers',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _requestPermission(context);
              },
              child: const Text('Enable'),
            ),
          ],
        );
      },
    );
  }

  static Future<void> _requestPermission(BuildContext context) async {
    try {
      bool granted = await NotificationService.requestNotificationPermission();
      
      if (!context.mounted) return;
      
      if (granted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Notifications enabled successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Check if permission was permanently denied
        if (Platform.isAndroid) {
          final status = await Permission.notification.status;
          if (status.isPermanentlyDenied) {
            _showSettingsDialog(context);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('❌ Notification permission denied'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  static void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permission Required'),
          content: const Text(
            'Notifications are disabled. Please enable them in Settings > Apps > FireFly > Notifications.',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  // Helper method to check and show permission dialog if needed
  static Future<void> checkAndRequestPermission(BuildContext context) async {
    try {
      bool hasPermission = await NotificationService.hasNotificationPermission();
      if (!hasPermission && context.mounted) {
        await showPermissionDialog(context);
      }
    } catch (e) {
      print('❌ Error checking notification permission: $e');
    }
  }
}