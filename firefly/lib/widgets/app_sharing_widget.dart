import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_rating_service.dart';
import '../utils/app_theme.dart';

class AppSharingWidget extends StatelessWidget {
  final String? customMessage;
  final VoidCallback? onShared;

  const AppSharingWidget({
    super.key,
    this.customMessage,
    this.onShared,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            'Share FireFly',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          Text(
            'Help others discover amazing wallpapers!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Sharing options
          Column(
            children: [
              // Share app button
              _buildShareOption(
                context: context,
                icon: Icons.share,
                title: 'Share App',
                subtitle: 'Share FireFly with friends',
                color: AppTheme.primaryBlue,
                onTap: () => _shareApp(context),
              ),
              const SizedBox(height: 12),

              // Rate and share button
              _buildShareOption(
                context: context,
                icon: Icons.star,
                title: 'Rate & Share',
                subtitle: 'Rate on Play Store and share',
                color: AppTheme.warningColor,
                onTap: () => _rateAndShare(context),
              ),
              const SizedBox(height: 12),

              // Copy link button
              _buildShareOption(
                context: context,
                icon: Icons.link,
                title: 'Copy Link',
                subtitle: 'Copy Play Store link',
                color: AppTheme.successColor,
                onTap: () => _copyLink(context),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Close button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.textSecondary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Close'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.borderColor),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.textTertiary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _shareApp(BuildContext context) async {
    try {
      await AppRatingService.shareApp(customMessage: customMessage);
      onShared?.call();
      if (context.mounted) {
        Navigator.of(context).pop();
        _showSuccessMessage(context, 'App shared successfully! 🚀');
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'Failed to share app: $e');
      }
    }
  }

  Future<void> _rateAndShare(BuildContext context) async {
    try {
      // First open rating
      await AppRatingService.showInAppRating();
      
      // Then share after a short delay
      await Future.delayed(const Duration(seconds: 1));
      await AppRatingService.shareApp(customMessage: customMessage);
      
      onShared?.call();
      if (context.mounted) {
        Navigator.of(context).pop();
        _showSuccessMessage(context, 'Thank you for rating and sharing! ❤️');
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'Failed to rate and share: $e');
      }
    }
  }

  Future<void> _copyLink(BuildContext context) async {
    try {
      // Get Play Store link and copy to clipboard
      const playStoreLink = 'https://play.google.com/store/apps/details?id=com.anuved.Firefly';
      
      await Clipboard.setData(const ClipboardData(text: playStoreLink));
      
      if (context.mounted) {
        Navigator.of(context).pop();
        _showSuccessMessage(context, 'Play Store link copied to clipboard! 📋');
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorMessage(context, 'Failed to copy link: $e');
      }
    }
  }

  void _showSuccessMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

/// Wallpaper sharing widget for sharing specific wallpapers
class WallpaperSharingWidget extends StatelessWidget {
  final String wallpaperTitle;
  final String wallpaperUrl;
  final String? customMessage;
  final VoidCallback? onShared;

  const WallpaperSharingWidget({
    super.key,
    required this.wallpaperTitle,
    required this.wallpaperUrl,
    this.customMessage,
    this.onShared,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppTheme.textTertiary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),

          // Title
          Text(
            'Share Wallpaper',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          Text(
            'Share "$wallpaperTitle" with friends!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 24),

          // Share button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _shareWallpaper(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              icon: const Icon(Icons.share),
              label: const Text(
                'Share Wallpaper',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),

          // Close button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: AppTheme.textSecondary,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Close'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _shareWallpaper(BuildContext context) async {
    try {
      await AppRatingService.shareWallpaper(
        wallpaperTitle: wallpaperTitle,
        wallpaperUrl: wallpaperUrl,
        customMessage: customMessage,
      );
      
      onShared?.call();
      if (context.mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Wallpaper shared successfully! 🎨'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share wallpaper: $e'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
}
