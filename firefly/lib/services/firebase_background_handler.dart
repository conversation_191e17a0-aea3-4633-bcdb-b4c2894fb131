import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  try {
    print('=== 🚀 BACKGROUND FCM MESSAGE HANDLER CALLED ===');
    print('📨 Background Message ID: ${message.messageId}');
    print('📨 Background Message from: ${message.from}');
    print('📨 Background Message data: ${message.data}');
    print('📨 Background Message sent time: ${message.sentTime}');
    
    // Initialize Firebase if not already initialized
    try {
      await Firebase.initializeApp();
      print('✅ Firebase initialized in background handler');
    } catch (e) {
      print('⚠️ Firebase already initialized or error: $e');
    }
    
    // Initialize local notifications for background handler
    try {
      await _initializeBackgroundLocalNotifications();
      print('✅ Background local notifications initialized');
    } catch (e) {
      print('⚠️ Background local notifications initialization failed: $e');
    }
    
    // Check if this is a Firebase Scheduled notification
    bool isFirebaseScheduled = message.from?.contains('firebase') == true || 
                              message.data['source'] == 'firebase_scheduled' ||
                              message.data['source'] == 'fcm_direct';
    
    if (isFirebaseScheduled) {
      print('🔥 Firebase Scheduled notification detected - processing directly');
    }
    
    // Background handler should only process data-only messages
    // Notification messages are automatically handled by the system and foreground handler
    if (message.notification != null) {
      print('📨 Background notification title: ${message.notification!.title}');
      print('📨 Background notification body: ${message.notification!.body}');
      print('⚠️ Notification payload detected - system will handle this automatically');
      print('⚠️ Skipping manual notification to prevent duplicates');
      // Don't show notification here - system handles it automatically
      // and foreground handler will handle it when app is active
    } else {
      print('⚠️ Background message has no notification payload, only data');
      if (message.data.isNotEmpty) {
        print('📨 Processing background data-only message: ${message.data}');
        // Only show notification for data-only messages
        await _showBackgroundLocalNotification(
          title: 'FireFly Update',
          body: 'New content available',
          payload: jsonEncode(message.data),
        );
        print('✅ Background data notification shown');
      }
    }
    
    print('=== ✅ BACKGROUND FCM MESSAGE HANDLER COMPLETED ===');
  } catch (e, stackTrace) {
    print('❌ ERROR in background FCM message handler: $e');
    print('Stack trace: $stackTrace');
    // Don't rethrow to prevent app crash
  }
}

// Initialize local notifications specifically for background handler
Future<void> _initializeBackgroundLocalNotifications() async {
  try {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/ic_notification');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: false, // Don't request in background
      requestBadgePermission: false,
      requestSoundPermission: false,
    );
    
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    final FlutterLocalNotificationsPlugin localNotifications = FlutterLocalNotificationsPlugin();
    await localNotifications.initialize(initializationSettings);
    
    // Create notification channel for Android
    if (Platform.isAndroid) {
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'firefly_notifications',
        'FireFly Notifications',
        description: 'Notifications for new wallpapers and updates',
        importance: Importance.high,
        enableVibration: true,
        playSound: true,
        showBadge: true,
      );
      
      await localNotifications
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    }
  } catch (e) {
    print('❌ Error initializing background local notifications: $e');
  }
}

// Show local notification from background handler
Future<void> _showBackgroundLocalNotification({
  required String title,
  required String body,
  String? payload,
  String? imageUrl,
}) async {
  try {
    final FlutterLocalNotificationsPlugin localNotifications = FlutterLocalNotificationsPlugin();
    
    AndroidNotificationDetails androidPlatformChannelSpecifics;
    
    if (imageUrl != null && imageUrl.isNotEmpty) {
      // Try to create big picture notification
      try {
        androidPlatformChannelSpecifics = AndroidNotificationDetails(
          'firefly_notifications',
          'FireFly Notifications',
          channelDescription: 'Notifications for new wallpapers and updates',
          importance: Importance.high,
          priority: Priority.high,
          styleInformation: BigTextStyleInformation(
            body,
            contentTitle: title,
          ),
          color: const Color(0xFF2196F3),
          playSound: true,
          enableVibration: true,
          showWhen: true,
          visibility: NotificationVisibility.public,
          category: AndroidNotificationCategory.social,
        );
      } catch (e) {
        print('Error creating big picture notification: $e');
        // Fallback to regular notification
        androidPlatformChannelSpecifics = const AndroidNotificationDetails(
          'firefly_notifications',
          'FireFly Notifications',
          channelDescription: 'Notifications for new wallpapers and updates',
          importance: Importance.high,
          priority: Priority.high,
          color: Color(0xFF2196F3),
          playSound: true,
          enableVibration: true,
          showWhen: true,
          visibility: NotificationVisibility.public,
          category: AndroidNotificationCategory.social,
        );
      }
    } else {
      // Regular notification without image
      androidPlatformChannelSpecifics = const AndroidNotificationDetails(
        'firefly_notifications',
        'FireFly Notifications',
        channelDescription: 'Notifications for new wallpapers and updates',
        importance: Importance.high,
        priority: Priority.high,
        color: Color(0xFF2196F3),
        playSound: true,
        enableVibration: true,
        showWhen: true,
        visibility: NotificationVisibility.public,
        category: AndroidNotificationCategory.social,
      );
    }
    
    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    
    final NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );
    
    await localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );
    
    print('✅ Background local notification shown: $title');
  } catch (e) {
    print('❌ Error showing background local notification: $e');
  }
}