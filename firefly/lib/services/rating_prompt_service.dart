import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_rating_service.dart';
import '../widgets/app_rating_dialog.dart';
import '../services/analytics_service.dart';

class RatingPromptService {
  static const String _lastPromptKey = 'last_rating_prompt_shown';
  static const String _sessionCountKey = 'app_session_count';
  static const String _wallpaperViewsKey = 'wallpaper_views_count';
  static const String _downloadsSessionKey = 'downloads_this_session';
  
  // Engagement thresholds for different prompt triggers
  static const int sessionsBeforeFirstPrompt = 3;
  static const int wallpaperViewsBeforePrompt = 10;
  static const int downloadsBeforePrompt = 2;
  static const int daysBeforeRetryPrompt = 7;

  /// Initialize rating prompt service (call on app start)
  static Future<void> initialize() async {
    try {
      await AppRatingService.incrementAppLaunchCount();
      await _incrementSessionCount();
      await _checkAndShowPrompt();
    } catch (e) {
      print('❌ Error initializing rating prompt service: $e');
    }
  }

  /// Track wallpaper view for rating prompt logic
  static Future<void> trackWallpaperView() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentViews = prefs.getInt(_wallpaperViewsKey) ?? 0;
      await prefs.setInt(_wallpaperViewsKey, currentViews + 1);
      
      // Check if we should show prompt after viewing wallpapers
      if ((currentViews + 1) % wallpaperViewsBeforePrompt == 0) {
        await _checkAndShowPrompt();
      }
    } catch (e) {
      print('❌ Error tracking wallpaper view: $e');
    }
  }

  /// Track wallpaper download for rating prompt logic
  static Future<void> trackWallpaperDownload() async {
    try {
      await AppRatingService.incrementDownloadCount();
      
      final prefs = await SharedPreferences.getInstance();
      final sessionDownloads = prefs.getInt(_downloadsSessionKey) ?? 0;
      await prefs.setInt(_downloadsSessionKey, sessionDownloads + 1);
      
      // Show prompt after user downloads wallpapers (they're engaged!)
      if ((sessionDownloads + 1) >= downloadsBeforePrompt) {
        await Future.delayed(const Duration(seconds: 2)); // Small delay
        await _checkAndShowPrompt();
      }
    } catch (e) {
      print('❌ Error tracking wallpaper download: $e');
    }
  }

  /// Show rating prompt with context
  static Future<void> showRatingPrompt(BuildContext context, {
    String? trigger,
  }) async {
    try {
      // Check if we should show the prompt
      if (!await AppRatingService.shouldShowRatingPrompt()) {
        print('📱 Rating prompt not eligible to show');
        return;
      }

      // Track the prompt trigger
      await AnalyticsService.trackCustomEvent(
        eventName: 'rating_prompt_triggered',
        parameters: {
          'trigger': trigger ?? 'manual',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // Show the rating dialog
      if (context.mounted) {
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AppRatingDialog(
            onRated: () async {
              await AnalyticsService.trackCustomEvent(
                eventName: 'rating_prompt_completed',
                parameters: {
                  'action': 'rated',
                  'trigger': trigger ?? 'manual',
                  'timestamp': DateTime.now().millisecondsSinceEpoch,
                },
              );
            },
            onDismissed: () async {
              await AnalyticsService.trackCustomEvent(
                eventName: 'rating_prompt_completed',
                parameters: {
                  'action': 'dismissed',
                  'trigger': trigger ?? 'manual',
                  'timestamp': DateTime.now().millisecondsSinceEpoch,
                },
              );
            },
          ),
        );
      }

      // Mark that we showed the prompt
      await _markPromptShown();
    } catch (e) {
      print('❌ Error showing rating prompt: $e');
    }
  }

  /// Show rating prompt card (less intrusive)
  static Widget? buildRatingPromptCard(BuildContext context) {
    return FutureBuilder<bool>(
      future: _shouldShowPromptCard(),
      builder: (context, snapshot) {
        if (snapshot.data == true) {
          return RatingPromptCard(
            onRatePressed: () async {
              await showRatingPrompt(context, trigger: 'prompt_card');
            },
            onDismissed: () async {
              await _markPromptShown();
              await AnalyticsService.trackCustomEvent(
                eventName: 'rating_prompt_card_dismissed',
                parameters: {
                  'timestamp': DateTime.now().millisecondsSinceEpoch,
                },
              );
            },
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// Check engagement and show prompt if appropriate
  static Future<void> _checkAndShowPrompt() async {
    try {
      if (!await AppRatingService.shouldShowRatingPrompt()) {
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final sessions = prefs.getInt(_sessionCountKey) ?? 0;
      final wallpaperViews = prefs.getInt(_wallpaperViewsKey) ?? 0;
      final downloads = prefs.getInt(_downloadsSessionKey) ?? 0;

      // Determine if user is engaged enough
      bool isEngaged = false;
      String? trigger;

      if (sessions >= sessionsBeforeFirstPrompt) {
        isEngaged = true;
        trigger = 'session_count';
      } else if (wallpaperViews >= wallpaperViewsBeforePrompt) {
        isEngaged = true;
        trigger = 'wallpaper_views';
      } else if (downloads >= downloadsBeforePrompt) {
        isEngaged = true;
        trigger = 'downloads';
      }

      if (isEngaged) {
        print('📱 User is engaged, scheduling rating prompt (trigger: $trigger)');
        
        // Track engagement milestone
        await AnalyticsService.trackCustomEvent(
          eventName: 'rating_prompt_eligible',
          parameters: {
            'trigger': trigger,
            'sessions': sessions,
            'wallpaper_views': wallpaperViews,
            'downloads': downloads,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          },
        );
      }
    } catch (e) {
      print('❌ Error checking rating prompt eligibility: $e');
    }
  }

  /// Check if we should show the less intrusive prompt card
  static Future<bool> _shouldShowPromptCard() async {
    try {
      if (!await AppRatingService.shouldShowRatingPrompt()) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final lastPrompt = prefs.getInt(_lastPromptKey) ?? 0;
      final daysSinceLastPrompt = DateTime.now().difference(
        DateTime.fromMillisecondsSinceEpoch(lastPrompt)
      ).inDays;

      // Show card if it's been a while since last prompt
      return daysSinceLastPrompt >= daysBeforeRetryPrompt;
    } catch (e) {
      print('❌ Error checking prompt card eligibility: $e');
      return false;
    }
  }

  /// Increment session count
  static Future<void> _incrementSessionCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentSessions = prefs.getInt(_sessionCountKey) ?? 0;
      await prefs.setInt(_sessionCountKey, currentSessions + 1);
      
      // Reset downloads per session
      await prefs.setInt(_downloadsSessionKey, 0);
      
      print('📱 Session count: ${currentSessions + 1}');
    } catch (e) {
      print('❌ Error incrementing session count: $e');
    }
  }

  /// Mark that we showed a rating prompt
  static Future<void> _markPromptShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastPromptKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      print('❌ Error marking prompt shown: $e');
    }
  }

  /// Get current engagement statistics
  static Future<Map<String, dynamic>> getEngagementStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final appStats = await AppRatingService.getEngagementStats();
      
      return {
        'sessions': prefs.getInt(_sessionCountKey) ?? 0,
        'wallpaper_views': prefs.getInt(_wallpaperViewsKey) ?? 0,
        'downloads_session': prefs.getInt(_downloadsSessionKey) ?? 0,
        'total_launches': appStats['launches'] ?? 0,
        'total_downloads': appStats['downloads'] ?? 0,
        'eligible_for_prompt': await AppRatingService.shouldShowRatingPrompt(),
      };
    } catch (e) {
      print('❌ Error getting engagement stats: $e');
      return {};
    }
  }

  /// Reset all engagement tracking (for testing)
  static Future<void> resetEngagementTracking() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastPromptKey);
      await prefs.remove(_sessionCountKey);
      await prefs.remove(_wallpaperViewsKey);
      await prefs.remove(_downloadsSessionKey);
      await AppRatingService.resetRatingPreferences();
      print('✅ Engagement tracking reset');
    } catch (e) {
      print('❌ Error resetting engagement tracking: $e');
    }
  }

  /// Show contextual rating prompt after positive user actions
  static Future<void> showContextualPrompt(BuildContext context, {
    required String action,
  }) async {
    try {
      // Only show contextual prompts for highly engaged actions
      if (action == 'wallpaper_set_successfully' || 
          action == 'multiple_downloads' ||
          action == 'app_shared') {
        
        // Small delay to let the user enjoy their success
        await Future.delayed(const Duration(seconds: 3));
        
        if (await AppRatingService.shouldShowRatingPrompt()) {
          await showRatingPrompt(context, trigger: 'contextual_$action');
        }
      }
    } catch (e) {
      print('❌ Error showing contextual prompt: $e');
    }
  }
}
