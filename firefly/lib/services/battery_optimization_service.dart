import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class BatteryOptimizationService {
  static const MethodChannel _channel = MethodChannel('battery_optimization');

  /// Check if battery optimization is disabled for the app
  static Future<bool> isBatteryOptimizationDisabled() async {
    try {
      final bool result = await _channel.invokeMethod('isBatteryOptimizationDisabled');
      return result;
    } catch (e) {
      print('❌ Error checking battery optimization: $e');
      return false;
    }
  }

  /// Request to disable battery optimization
  static Future<bool> requestDisableBatteryOptimization() async {
    try {
      final bool result = await _channel.invokeMethod('requestDisableBatteryOptimization');
      return result;
    } catch (e) {
      print('❌ Error requesting battery optimization disable: $e');
      return false;
    }
  }

  /// Check if app can schedule exact alarms (Android 12+)
  static Future<bool> canScheduleExactAlarms() async {
    try {
      final bool result = await _channel.invokeMethod('canScheduleExactAlarms');
      return result;
    } catch (e) {
      print('❌ Error checking exact alarm permission: $e');
      return false;
    }
  }

  /// Request exact alarm permission (Android 12+)
  static Future<bool> requestExactAlarmPermission() async {
    try {
      final bool result = await _channel.invokeMethod('requestExactAlarmPermission');
      return result;
    } catch (e) {
      print('❌ Error requesting exact alarm permission: $e');
      return false;
    }
  }

  /// Show dialog to guide user to disable battery optimization
  static Future<void> showBatteryOptimizationDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1a1a1a),
          title: const Text(
            'Enable Reliable Notifications',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'To receive notifications reliably, please:',
                style: TextStyle(color: Colors.white70, fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildStep('1', 'Disable battery optimization for FireFly'),
              const SizedBox(height: 8),
              _buildStep('2', 'Allow background activity'),
              const SizedBox(height: 8),
              _buildStep('3', 'Enable auto-start (if available)'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This ensures you receive wallpaper updates and notifications even when the app is closed.',
                        style: TextStyle(color: Colors.orange, fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Later',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await requestDisableBatteryOptimization();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
              ),
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  static Widget _buildStep(String number, String text) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: const Color(0xFF667eea),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              number,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
        ),
      ],
    );
  }

  /// Check and request all necessary permissions for reliable notifications
  static Future<bool> ensureReliableNotifications(BuildContext context) async {
    try {
      print('🔋 Checking battery optimization status...');
      
      // Check battery optimization
      bool isBatteryOptDisabled = await isBatteryOptimizationDisabled();
      print('🔋 Battery optimization disabled: $isBatteryOptDisabled');
      
      // Check exact alarm permission (Android 12+)
      bool canScheduleAlarms = await canScheduleExactAlarms();
      print('⏰ Can schedule exact alarms: $canScheduleAlarms');
      
      // If either is missing, show dialog
      if (!isBatteryOptDisabled || !canScheduleAlarms) {
        if (context.mounted) {
          await showBatteryOptimizationDialog(context);
        }
        return false;
      }
      
      return true;
    } catch (e) {
      print('❌ Error ensuring reliable notifications: $e');
      return false;
    }
  }

  /// Request notification permission with fallback
  static Future<bool> requestNotificationPermission() async {
    try {
      // Request POST_NOTIFICATIONS permission for Android 13+
      PermissionStatus status = await Permission.notification.request();
      
      if (status.isGranted) {
        print('✅ Notification permission granted');
        return true;
      } else if (status.isDenied) {
        print('❌ Notification permission denied');
        return false;
      } else if (status.isPermanentlyDenied) {
        print('❌ Notification permission permanently denied');
        // Open app settings
        await openAppSettings();
        return false;
      }
      
      return false;
    } catch (e) {
      print('❌ Error requesting notification permission: $e');
      return false;
    }
  }

  /// Show notification settings guide
  static Future<void> showNotificationSettingsGuide(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1a1a1a),
          title: const Text(
            'Notification Settings',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'For the best notification experience:',
                style: TextStyle(color: Colors.white70, fontSize: 16),
              ),
              const SizedBox(height: 16),
              _buildSettingItem(
                Icons.notifications_active,
                'Enable Notifications',
                'Allow FireFly to send notifications',
              ),
              const SizedBox(height: 12),
              _buildSettingItem(
                Icons.battery_saver_outlined,
                'Disable Battery Optimization',
                'Prevent Android from limiting background activity',
              ),
              const SizedBox(height: 12),
              _buildSettingItem(
                Icons.autorenew,
                'Enable Auto-start',
                'Allow app to start automatically (varies by device)',
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Close',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await openAppSettings();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
              ),
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  static Widget _buildSettingItem(IconData icon, String title, String subtitle) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFF667eea), size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  color: Colors.white60,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
