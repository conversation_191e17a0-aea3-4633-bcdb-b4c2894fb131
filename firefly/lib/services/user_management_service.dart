import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

class UserManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  // Create or update user document in Firestore
  static Future<void> createOrUpdateUser(User user) async {
    try {
      final String userId = user.uid;
      final DocumentReference userDoc = _firestore.collection('users').doc(userId);
      
      // Check if user document exists
      final DocumentSnapshot userSnapshot = await userDoc.get();
      final bool isNewUser = !userSnapshot.exists;
      
      // Get device information
      final Map<String, dynamic> deviceInfo = await _getDeviceInfo();
      
      // Prepare user data
      final Map<String, dynamic> userData = {
        'userId': userId,
        'lastActive': FieldValue.serverTimestamp(),
        'isAnonymous': user.isAnonymous,
        'deviceInfo': deviceInfo,
      };
      
      // Add creation timestamp for new users
      if (isNewUser) {
        userData['createdAt'] = FieldValue.serverTimestamp();
        print('📝 Creating new user document for ${user.isAnonymous ? 'anonymous' : 'authenticated'} user: $userId');
      } else {
        print('📝 Updating existing user document for user: $userId');
      }
      
      // Add email for authenticated users
      if (!user.isAnonymous && user.email != null) {
        userData['email'] = user.email;
        userData['displayName'] = user.displayName;
        userData['photoURL'] = user.photoURL;
      }
      
      // Use merge to avoid overwriting existing data
      await userDoc.set(userData, SetOptions(merge: true));
      
      print('✅ User document ${isNewUser ? 'created' : 'updated'} successfully for user: $userId');
      
      // Initialize user preferences for new users
      if (isNewUser) {
        await _initializeUserPreferences(userId);
      }
      
    } catch (e) {
      print('❌ Error creating/updating user document: $e');
    }
  }
  
  // Update user's last active timestamp
  static Future<void> updateLastActive(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'lastActive': FieldValue.serverTimestamp(),
      });
      print('📝 Updated last active for user: $userId');
    } catch (e) {
      print('❌ Error updating last active: $e');
    }
  }
  
  // Get user document
  static Future<DocumentSnapshot?> getUserDocument(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore.collection('users').doc(userId).get();
      return doc.exists ? doc : null;
    } catch (e) {
      print('❌ Error getting user document: $e');
      return null;
    }
  }
  
  // Initialize user preferences
  static Future<void> _initializeUserPreferences(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).collection('preferences').doc('settings').set({
        'theme': 'system',
        'downloadQuality': 'hd',
        'autoWallpaper': false,
        'notifications': true,
        'createdAt': FieldValue.serverTimestamp(),
      });
      
      print('✅ User preferences initialized for user: $userId');
    } catch (e) {
      print('❌ Error initializing user preferences: $e');
    }
  }
  
  // Get device information
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final Map<String, dynamic> deviceData = {};
      
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await _deviceInfo.androidInfo;
        deviceData.addAll({
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
        });
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await _deviceInfo.iosInfo;
        deviceData.addAll({
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemVersion': iosInfo.systemVersion,
          'localizedModel': iosInfo.localizedModel,
        });
      } else {
        deviceData['platform'] = 'unknown';
      }
      
      return deviceData;
    } catch (e) {
      print('❌ Error getting device info: $e');
      return {'platform': 'unknown', 'error': e.toString()};
    }
  }
  
  // Track user activity
  static Future<void> trackUserActivity({
    required String userId,
    required String activity,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _firestore.collection('users').doc(userId).collection('activity').add({
        'activity': activity,
        'timestamp': FieldValue.serverTimestamp(),
        'metadata': metadata ?? {},
      });
      
      // Also update last active
      await updateLastActive(userId);
      
      print('📊 User activity tracked: $activity for user: $userId');
    } catch (e) {
      print('❌ Error tracking user activity: $e');
    }
  }
  
  // Get user statistics
  static Future<Map<String, dynamic>?> getUserStats(String userId) async {
    try {
      final DocumentSnapshot statsDoc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('summary')
          .get();
      
      if (statsDoc.exists) {
        return statsDoc.data() as Map<String, dynamic>?;
      }
      
      // Initialize stats if they don't exist
      await _initializeUserStats(userId);
      return {
        'wallpapersViewed': 0,
        'wallpapersDownloaded': 0,
        'favoritesCount': 0,
        'lastUpdated': FieldValue.serverTimestamp(),
      };
    } catch (e) {
      print('❌ Error getting user stats: $e');
      return null;
    }
  }
  
  // Initialize user statistics
  static Future<void> _initializeUserStats(String userId) async {
    try {
      await _firestore.collection('users').doc(userId).collection('stats').doc('summary').set({
        'wallpapersViewed': 0,
        'wallpapersDownloaded': 0,
        'favoritesCount': 0,
        'createdAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });
      
      print('✅ User stats initialized for user: $userId');
    } catch (e) {
      print('❌ Error initializing user stats: $e');
    }
  }
  
  // Update user statistics
  static Future<void> updateUserStats({
    required String userId,
    String? incrementField,
    Map<String, dynamic>? customUpdates,
  }) async {
    try {
      final DocumentReference statsDoc = _firestore
          .collection('users')
          .doc(userId)
          .collection('stats')
          .doc('summary');
      
      final Map<String, dynamic> updates = {
        'lastUpdated': FieldValue.serverTimestamp(),
      };
      
      if (incrementField != null) {
        updates[incrementField] = FieldValue.increment(1);
      }
      
      if (customUpdates != null) {
        updates.addAll(customUpdates);
      }
      
      await statsDoc.update(updates);
      print('📊 User stats updated for user: $userId');
    } catch (e) {
      // If document doesn't exist, create it first
      if (e.toString().contains('No document to update')) {
        await _initializeUserStats(userId);
        // Retry the update
        await updateUserStats(
          userId: userId,
          incrementField: incrementField,
          customUpdates: customUpdates,
        );
      } else {
        print('❌ Error updating user stats: $e');
      }
    }
  }
  
  // Clean up old anonymous users (utility function for maintenance)
  static Future<void> cleanupOldAnonymousUsers({int daysOld = 30}) async {
    try {
      final DateTime cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      final Timestamp cutoffTimestamp = Timestamp.fromDate(cutoffDate);
      
      final QuerySnapshot oldUsers = await _firestore
          .collection('users')
          .where('isAnonymous', isEqualTo: true)
          .where('lastActive', isLessThan: cutoffTimestamp)
          .limit(100) // Process in batches
          .get();
      
      final WriteBatch batch = _firestore.batch();
      
      for (final doc in oldUsers.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      print('🧹 Cleaned up ${oldUsers.docs.length} old anonymous users');
    } catch (e) {
      print('❌ Error cleaning up old anonymous users: $e');
    }
  }
}
