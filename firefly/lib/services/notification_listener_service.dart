import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

class NotificationListenerService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  static StreamSubscription<QuerySnapshot>? _notificationSubscription;
  static String? _currentUserToken;
  static bool _isEnabled = true; // Flag to enable/disable listener

  /// Initialize the notification listener
  static Future<void> initialize() async {
    // Get current user's FCM token
    _currentUserToken = await FirebaseMessaging.instance.getToken();
    
    // Initialize local notifications
    await _initializeLocalNotifications();
    
    // Check for any missed notifications first
    await checkMissedNotifications();
    
    // Start listening for new notifications only if enabled
    if (_isEnabled) {
      _startListening();
    }
  }
  
  /// Enable or disable the notification listener
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (enabled) {
      _startListening();
    } else {
      _notificationSubscription?.cancel();
      _notificationSubscription = null;
      print('NotificationListenerService disabled');
    }
  }
  
  /// Stop the notification listener
  static void dispose() {
    _notificationSubscription?.cancel();
    _notificationSubscription = null;
  }

  /// Initialize local notifications plugin
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    
    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
    
    // Create notification channel for Android
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'firefly_notifications',
      'Firefly Notifications',
      description: 'Notifications for Firefly Wallpaper App',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
      showBadge: true,
    );
    
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
    
    print('Local notifications initialized with channel');
  }

  /// Start listening for new notifications from Firestore
  static void _startListening() {
    // Listen to notifications from the last 5 minutes to catch recent ones
    final fiveMinutesAgo = Timestamp.fromDate(
      DateTime.now().subtract(const Duration(minutes: 5)),
    );
    
    _notificationSubscription = _firestore
        .collection('live_notifications')
        .where('timestamp', isGreaterThan: fiveMinutesAgo)
        .orderBy('timestamp')
        .snapshots()
        .listen((snapshot) {
      for (var change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added) {
          try {
            final data = change.doc.data() as Map<String, dynamic>;
            
            // Check if this is a new notification (not older than 30 seconds)
            final timestamp = data['timestamp'] as Timestamp?;
            if (timestamp != null) {
              final notificationTime = timestamp.toDate();
              final now = DateTime.now();
              final difference = now.difference(notificationTime).inSeconds;
              
              if (difference > 30) {
                print('⏰ Skipping old notification (${difference}s old)');
                continue;
              }
            }
            
            print('📨 New notification received: ${data['title']}');
            print('📨 Notification data: $data');
            
            _handleNewNotification(change.doc);
            
            // Mark notification as processed
            try {
              change.doc.reference.update({
                'processed': true,
                'processedAt': FieldValue.serverTimestamp(),
              });
            } catch (e) {
              print('⚠️ Could not mark notification as processed: $e');
            }
          } catch (e) {
            print('❌ Error processing notification: $e');
          }
        }
      }
    }, onError: (error) {
      print('Error in notification listener: $error');
      // Restart listener after a delay
      Future.delayed(const Duration(seconds: 5), () {
        if (_isEnabled) {
          dispose();
          _startListening();
        }
      });
      // Fallback: listen to all notifications but filter in handler
      _startListeningFallback();
    });
  }
  
  /// Fallback listener without source filter (for older Firestore versions)
  static void _startListeningFallback() {
    _notificationSubscription?.cancel();
    
    // Listen to notifications from the last 5 minutes to catch recent ones
    final fiveMinutesAgo = Timestamp.fromDate(
      DateTime.now().subtract(const Duration(minutes: 5)),
    );
    
    _notificationSubscription = _firestore
        .collection('live_notifications')
        .where('timestamp', isGreaterThan: fiveMinutesAgo)
        .snapshots()
        .listen((snapshot) {
      for (var change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added) {
          _handleNewNotification(change.doc);
        }
      }
    });
  }

  /// Handle new notification from Firestore
  static void _handleNewNotification(DocumentSnapshot doc) {
    try {
      final data = doc.data() as Map<String, dynamic>;
      final target = data['target'] as String?;
      final title = data['title'] as String? ?? 'Notification';
      final body = data['body'] as String? ?? '';
      final source = data['source'] as String? ?? 'unknown';
      final delivered = data['delivered'] as bool? ?? false;
      
      // Skip if already delivered
      if (delivered) {
        print('Skipping already delivered notification: $title');
        return;
      }
      
      // Only process admin panel notifications
      if (source != 'admin_panel') {
        print('Skipping non-admin notification: $source');
        return;
      }
      
      // Skip notifications that are from Firebase scheduled messages
      // to avoid duplicates with FCM direct notifications
      if (source == 'firebase_scheduled' || source == 'fcm_direct') {
        print('Skipping Firestore notification to avoid duplicate: $source');
        // Mark as delivered but don't show notification
        doc.reference.update({'delivered': true, 'skipped_duplicate': true});
        return;
      }
      
      // Check if this notification is for current user
      bool shouldShow = false;
      
      if (target == 'all_users' || target == 'all') {
        shouldShow = true;
      } else if (target == _currentUserToken) {
        shouldShow = true;
      } else if (target?.startsWith('topic_') == true) {
        // For topic-based notifications, we assume user is subscribed
        // In a real app, you'd check user's topic subscriptions
        shouldShow = true;
      }
      
      if (shouldShow) {
        print('Showing Firestore notification: $title');
        _showLocalNotification(
          title: title,
          body: body,
          payload: doc.id,
          imageUrl: data['imageUrl'] as String?,
        );
        
        // Mark as delivered
        doc.reference.update({'delivered': true});
      }
    } catch (e) {
      print('Error handling notification: $e');
    }
  }

  /// Show local notification
  static Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? payload,
  }) async {
    try {
      print('🔔 Showing local notification: $title');
      print('🔔 Notification body: $body');
      print('🔔 Image URL: $imageUrl');
      print('🔔 Payload: $payload');
      
      AndroidNotificationDetails androidPlatformChannelSpecifics;
      
      if (imageUrl != null && imageUrl.isNotEmpty) {
        // Try big picture notification with image
        try {
          androidPlatformChannelSpecifics = AndroidNotificationDetails(
            'firefly_notifications',
            'FireFly Notifications',
            channelDescription: 'Notifications for new wallpapers and updates',
            importance: Importance.high,
            priority: Priority.high,
            styleInformation: BigTextStyleInformation(
              body,
              contentTitle: title,
            ),
            color: const Color(0xFF2196F3),
            playSound: true,
            enableVibration: true,
            showWhen: true,
            visibility: NotificationVisibility.public,
            category: AndroidNotificationCategory.social,
            fullScreenIntent: false,
            autoCancel: true,
          );
          print('✅ Created big picture notification');
        } catch (e) {
          print('⚠️ Error creating big picture notification: $e, falling back to regular');
          // Fallback to regular notification
          androidPlatformChannelSpecifics = const AndroidNotificationDetails(
            'firefly_notifications',
            'FireFly Notifications',
            channelDescription: 'Notifications for new wallpapers and updates',
            importance: Importance.high,
            priority: Priority.high,
            color: Color(0xFF2196F3),
            playSound: true,
            enableVibration: true,
            showWhen: true,
            visibility: NotificationVisibility.public,
            category: AndroidNotificationCategory.social,
            autoCancel: true,
          );
        }
      } else {
        // Regular notification without image
        androidPlatformChannelSpecifics = const AndroidNotificationDetails(
          'firefly_notifications',
          'FireFly Notifications',
          channelDescription: 'Notifications for new wallpapers and updates',
          importance: Importance.high,
          priority: Priority.high,
          color: Color(0xFF2196F3),
          playSound: true,
          enableVibration: true,
          showWhen: true,
          visibility: NotificationVisibility.public,
          category: AndroidNotificationCategory.social,
          autoCancel: true,
        );
      }
      
      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        categoryIdentifier: 'firefly_category',
      );
      
      final NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );
      
      final notificationId = DateTime.now().millisecondsSinceEpoch.remainder(100000);
      print('🔔 Showing notification with ID: $notificationId');
      
      await _localNotifications.show(
        notificationId,
        title,
        body,
        platformChannelSpecifics,
        payload: payload,
      );
      
      print('✅ Local notification shown successfully with ID: $notificationId');
      
      // Verify notification was shown
      final pendingNotifications = await _localNotifications.pendingNotificationRequests();
      print('📱 Pending notifications count: ${pendingNotifications.length}');
      
    } catch (e, stackTrace) {
      print('❌ Error showing local notification: $e');
      print('Stack trace: $stackTrace');
      
      // Try to show a basic notification as fallback
      try {
        await _localNotifications.show(
          DateTime.now().millisecondsSinceEpoch.remainder(100000),
          'FireFly',
          'New content available',
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'firefly_notifications',
              'FireFly Notifications',
              importance: Importance.high,
              priority: Priority.high,
            ),
          ),
        );
        print('✅ Fallback notification shown');
      } catch (fallbackError) {
        print('❌ Fallback notification also failed: $fallbackError');
      }
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap - navigate to appropriate screen
    print('Notification tapped: ${response.payload}');
    // You can add navigation logic here
  }



  /// Manually check for missed notifications
  static Future<void> checkMissedNotifications() async {
    try {
      final oneHourAgo = Timestamp.fromDate(
        DateTime.now().subtract(const Duration(hours: 1)),
      );
      
      print('Checking for missed notifications from admin panel...');
      
      final querySnapshot = await _firestore
          .collection('live_notifications')
          .where('timestamp', isGreaterThan: oneHourAgo)
          .where('delivered', isEqualTo: false)
          .orderBy('timestamp')
          .get();
      
      print('Found ${querySnapshot.docs.length} undelivered notifications');
      
      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        final source = data['source'] as String? ?? 'unknown';
        if (source == 'admin_panel') {
          _handleNewNotification(doc);
        }
      }
    } catch (e) {
      print('Error checking missed notifications: $e');
      // Simple fallback - get recent notifications and filter
      try {
        final oneHourAgo = Timestamp.fromDate(
          DateTime.now().subtract(const Duration(hours: 1)),
        );
        
        final querySnapshot = await _firestore
            .collection('live_notifications')
            .where('timestamp', isGreaterThan: oneHourAgo)
            .orderBy('timestamp')
            .get();
        
        for (var doc in querySnapshot.docs) {
          final data = doc.data();
          final source = data['source'] as String? ?? 'unknown';
          final delivered = data['delivered'] as bool? ?? false;
          if (source == 'admin_panel' && !delivered) {
            _handleNewNotification(doc);
          }
        }
      } catch (fallbackError) {
        print('Fallback missed notifications check also failed: $fallbackError');
      }
    }
  }
}