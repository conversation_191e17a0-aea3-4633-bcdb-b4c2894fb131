import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'user_management_service.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Initialize analytics
  static Future<void> initialize() async {
    try {
      // Set analytics collection enabled
      await _analytics.setAnalyticsCollectionEnabled(true);

      // Set user properties for anonymous users
      final User? user = _auth.currentUser;
      if (user != null) {
        await _setUserProperties(user);
      }

      print('✅ Analytics service initialized');
    } catch (e) {
      print('❌ Error initializing analytics: $e');
    }
  }

  // Set user properties
  static Future<void> _setUserProperties(User user) async {
    try {
      await _analytics.setUserId(id: user.uid);

      // Set user properties
      await _analytics.setUserProperty(
        name: 'user_type',
        value: user.isAnonymous ? 'anonymous' : 'authenticated',
      );

      if (!user.isAnonymous && user.email != null) {
        await _analytics.setUserProperty(
          name: 'user_email',
          value: user.email!,
        );
      }

      print('✅ User properties set for analytics');
    } catch (e) {
      print('❌ Error setting user properties: $e');
    }
  }

  // Update user properties when auth state changes
  static Future<void> updateUserProperties(User? user) async {
    if (user != null) {
      await _setUserProperties(user);
    } else {
      // Clear user properties when signed out
      await _analytics.setUserId(id: null);
    }
  }

  // Track app open event
  static Future<void> trackAppOpen() async {
    try {
      String userType = 'unauthenticated';
      String? userId;

      if (_auth.currentUser != null) {
        userType = _auth.currentUser!.isAnonymous
            ? 'anonymous'
            : 'authenticated';
        userId = _auth.currentUser!.uid;
      } else {
        // Create or get local user session for analytics
        userId = await UserManagementService.getOrCreateLocalUserSession();
        userType = 'local';

        // Update local stats
        await UserManagementService.updateLocalUserStats('app_opens');
      }

      await _analytics.logEvent(
        name: 'app_open',
        parameters: {
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'user_type': userType,
          'user_id': userId,
        },
      );
      print('📊 Analytics: app_open event tracked for $userType user');
    } catch (e) {
      print('❌ Error tracking app_open: $e');
    }
  }

  // Track wallpaper view event
  static Future<void> trackWallpaperView({
    required String wallpaperId,
    required String category,
    String? source,
  }) async {
    try {
      String userType = 'unauthenticated';
      String? userId;

      if (_auth.currentUser != null) {
        userType = _auth.currentUser!.isAnonymous
            ? 'anonymous'
            : 'authenticated';
        userId = _auth.currentUser!.uid;
      } else {
        // Get local user session for analytics
        userId = await UserManagementService.getOrCreateLocalUserSession();
        userType = 'local';

        // Update local stats
        await UserManagementService.updateLocalUserStats('wallpapers_viewed');
      }

      await _analytics.logEvent(
        name: 'wallpaper_view',
        parameters: {
          'wallpaper_id': wallpaperId,
          'category': category,
          'source': source ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'user_type': userType,
          'user_id': userId,
        },
      );
      print(
        '📊 Analytics: wallpaper_view event tracked for $wallpaperId ($userType user)',
      );
    } catch (e) {
      print('❌ Error tracking wallpaper_view: $e');
    }
  }

  // Track wallpaper download event
  static Future<void> trackWallpaperDownload({
    required String wallpaperId,
    required String category,
    String? quality,
    String? source,
  }) async {
    try {
      String userType = 'unauthenticated';
      String? userId;

      if (_auth.currentUser != null) {
        userType = _auth.currentUser!.isAnonymous
            ? 'anonymous'
            : 'authenticated';
        userId = _auth.currentUser!.uid;
      } else {
        // Get local user session for analytics
        userId = await UserManagementService.getOrCreateLocalUserSession();
        userType = 'local';

        // Update local stats
        await UserManagementService.updateLocalUserStats(
          'wallpapers_downloaded',
        );
      }

      await _analytics.logEvent(
        name: 'wallpaper_download',
        parameters: {
          'wallpaper_id': wallpaperId,
          'category': category,
          'quality': quality ?? 'hd',
          'source': source ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'user_type': userType,
          'user_id': userId,
        },
      );
      print(
        '📊 Analytics: wallpaper_download event tracked for $wallpaperId ($userType user)',
      );
    } catch (e) {
      print('❌ Error tracking wallpaper_download: $e');
    }
  }

  // Track user sign in event
  static Future<void> trackUserSignIn({
    required String signInMethod,
    bool isAdmin = false,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'login',
        parameters: {
          'method': signInMethod,
          'is_admin': isAdmin,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
      print('📊 Analytics: login event tracked with method $signInMethod');
    } catch (e) {
      print('❌ Error tracking login: $e');
    }
  }

  // Track user sign out event
  static Future<void> trackUserSignOut() async {
    try {
      await _analytics.logEvent(
        name: 'logout',
        parameters: {'timestamp': DateTime.now().millisecondsSinceEpoch},
      );
      print('📊 Analytics: logout event tracked');
    } catch (e) {
      print('❌ Error tracking logout: $e');
    }
  }

  // Track screen view
  static Future<void> trackScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? screenName,
      );
      print('📊 Analytics: screen_view tracked for $screenName');
    } catch (e) {
      print('❌ Error tracking screen_view: $e');
    }
  }

  // Track custom event
  static Future<void> trackCustomEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: {
          ...?parameters,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'user_type': _auth.currentUser?.isAnonymous == true
              ? 'anonymous'
              : 'authenticated',
        },
      );
      print('📊 Analytics: custom event $eventName tracked');
    } catch (e) {
      print('❌ Error tracking custom event $eventName: $e');
    }
  }

  // Track app rating events
  static Future<void> trackAppRating({
    required String action, // 'prompt_shown', 'rated', 'dismissed', 'never_ask'
    String? trigger, // 'session_count', 'downloads', 'manual', etc.
    String? ratingType, // 'in_app_review', 'play_store'
  }) async {
    try {
      await trackCustomEvent(
        eventName: 'app_rating',
        parameters: {
          'action': action,
          'trigger': trigger,
          'rating_type': ratingType,
          'platform': 'android',
        },
      );
    } catch (e) {
      print('❌ Error tracking app rating: $e');
    }
  }

  // Track app sharing events
  static Future<void> trackAppSharing({
    required String action, // 'app_shared', 'wallpaper_shared', 'link_copied'
    String? shareMethod, // 'native_share', 'social_media', 'clipboard'
    String? source, // 'drawer', 'preview_screen', 'prompt'
    String? wallpaperId,
    String? wallpaperTitle,
  }) async {
    try {
      await trackCustomEvent(
        eventName: 'app_sharing',
        parameters: {
          'action': action,
          'share_method': shareMethod,
          'source': source,
          'wallpaper_id': wallpaperId,
          'wallpaper_title': wallpaperTitle,
          'platform': 'android',
        },
      );
    } catch (e) {
      print('❌ Error tracking app sharing: $e');
    }
  }

  // Track user engagement for rating prompts
  static Future<void> trackUserEngagement({
    required String
    engagementType, // 'session_start', 'wallpaper_view', 'download', 'share'
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      await trackCustomEvent(
        eventName: 'user_engagement',
        parameters: {'engagement_type': engagementType, ...?additionalData},
      );
    } catch (e) {
      print('❌ Error tracking user engagement: $e');
    }
  }

  // Get analytics instance for advanced usage
  static FirebaseAnalytics get instance => _analytics;
}
