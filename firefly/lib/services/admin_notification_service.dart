import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:dio/dio.dart';
import 'package:googleapis_auth/auth_io.dart';
import '../config/firebase_config.dart';

class AdminNotificationService {
  // Note: The legacy server key API is deprecated. We'll use Firebase Admin SDK approach
  // For now, using Firebase Messaging directly for topic messaging
  static const String _projectId = 'firefly-8cbce'; // Correct Firebase project ID
  
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Send notification to all users using Firebase Messaging
  static Future<bool> sendNotificationToAll({
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // Create the message
      final message = RemoteMessage(
        data: {
          'screen': 'home',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          'title': title,
          'body': body,
          ...?data,
        },
      );

      // For admin notifications, we'll use a simpler approach
      // Save to Firestore and let clients listen for new notifications
      await _saveNotificationToHistory(title, body, 'all_users', data, imageUrl: imageUrl);
      
      // Also save to a 'live_notifications' collection for real-time updates
      await _firestore.collection('live_notifications').add({
        'title': title,
        'body': body,
        'target': 'all_users',
        'data': data ?? {},
        'imageUrl': imageUrl,
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
        'delivered': false,
        'source': 'admin_panel', // Mark as admin panel notification
      });
      
      // Send FCM message to all users topic for background notifications
      await _sendFCMToAllUsers(
        title: title,
        body: body,
        imageUrl: imageUrl,
        data: data,
      );
      
      print('Notification saved successfully for all users');
      return true;
    } catch (e) {
      print('Error sending notification to all users: $e');
      return false;
    }
  }

  /// Send notification to specific topic using Firestore
  static Future<bool> sendNotificationToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // Save to Firestore for topic-based notifications
      await _saveNotificationToHistory(title, body, topic, data, imageUrl: imageUrl);
      
      // Also save to live notifications for real-time updates
      await _firestore.collection('live_notifications').add({
        'title': title,
        'body': body,
        'target': topic,
        'data': data ?? {},
        'imageUrl': imageUrl,
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
        'delivered': false,
        'source': 'admin_panel', // Mark as admin panel notification
      });
      
      print('Notification saved successfully for topic: $topic');
      return true;
    } catch (e) {
      print('Error sending notification to topic $topic: $e');
      return false;
    }
  }

  /// Send notification to specific user using Firestore
  static Future<bool> sendNotificationToUser({
    required String fcmToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) async {
    try {
      // Save to notification history
      await _saveNotificationToHistory(title, body, 'specific_user', data, imageUrl: imageUrl);
      
      // Save to live notifications for real-time updates
      await _firestore.collection('live_notifications').add({
        'title': title,
        'body': body,
        'target': fcmToken,
        'source': 'admin_panel', // Mark as admin panel notification
        'data': data ?? {},
        'imageUrl': imageUrl,
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
        'delivered': false,
      });
      
      print('Notification saved successfully for user');
      return true;
    } catch (e) {
      print('Error sending notification to user: $e');
      return false;
    }
  }

  /// Send notification about new wallpaper
  static Future<bool> sendNewWallpaperNotification({
    required String wallpaperTitle,
    required String category,
    String? wallpaperUrl,
  }) async {
    return await sendNotificationToAll(
      title: 'New Wallpaper Added! 🎨',
      body: 'Check out "$wallpaperTitle" in $category category',
      data: {
        'screen': 'category',
        'category_name': category,
        'wallpaper_title': wallpaperTitle,
        'source': 'fcm_direct',
      },
      imageUrl: wallpaperUrl,
    );
  }

  /// Send FCM message to all users topic
  static Future<void> _sendFCMToAllUsers({
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      print('🚀 Sending FCM message to all_users topic');
      
      // Prepare FCM message data
      final fcmData = {
        'title': title,
        'body': body,
        'click_action': 'FLUTTER_NOTIFICATION_CLICK',
        'source': 'fcm_direct',
        'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        ...?data,
      };
      
      if (imageUrl != null) {
        fcmData['image'] = imageUrl;
      }
      
      // Create FCM message
      final message = {
        'to': '/topics/all_users',
        'notification': {
          'title': title,
          'body': body,
          if (imageUrl != null) 'image': imageUrl,
        },
        'data': fcmData,
        'android': {
          'notification': {
            'channel_id': 'firefly_notifications',
            'priority': 'high',
            'visibility': 'public',
            'color': '#2196F3',
            'sound': 'default',
            if (imageUrl != null) 'image': imageUrl,
          },
        },
        'apns': {
          'payload': {
            'aps': {
              'alert': {
                'title': title,
                'body': body,
              },
              'sound': 'default',
              'badge': 1,
            },
          },
          if (imageUrl != null) 'fcm_options': {'image': imageUrl},
        },
      };
      
      // Send actual FCM HTTP request
      await _sendFCMHttpRequest(message);
      print('✅ FCM message sent to all_users topic');
      
    } catch (e) {
      print('❌ Error sending FCM message: $e');
      // Don't throw to prevent breaking the notification flow
    }
  }

  /// Send FCM HTTP v1 request using OAuth 2.0
  static Future<void> _sendFCMHttpRequest(Map<String, dynamic> legacyMessage) async {
    try {
      print('🚀 Starting FCM HTTP v1 request...');
      
      // Check if service account is configured
      if (FirebaseConfig.serviceAccountKeyJson.contains('YOUR_PROJECT_ID')) {
        print('⚠️ Service Account Key not configured. Please add your service account key in firebase_config.dart');
        print('📝 Get your service account key from: Firebase Console > Project Settings > Service Accounts > Generate New Private Key');
        return;
      }
      
      // Get OAuth 2.0 access token
      print('🔑 Getting OAuth access token...');
      final accessToken = await _getAccessToken();
      if (accessToken == null) {
        print('❌ Failed to get OAuth access token');
        return;
      }
      print('✅ OAuth access token obtained');
      
      // Extract project ID from service account
      final serviceAccount = jsonDecode(FirebaseConfig.serviceAccountKeyJson);
      final projectId = serviceAccount['project_id'];
      print('📋 Project ID: $projectId');
      
      // Convert legacy message format to HTTP v1 format
      final v1Message = _convertToV1Format(legacyMessage);
      print('📝 Message converted to v1 format');
      print('📋 Target topic: ${v1Message['topic']}');
      
      final dio = Dio();
      final url = 'https://fcm.googleapis.com/v1/projects/$projectId/messages:send';
      print('🌐 Sending request to: $url');
      
      final response = await dio.post(
        url,
        options: Options(
          headers: {
            'Authorization': 'Bearer $accessToken',
            'Content-Type': 'application/json',
          },
        ),
        data: {'message': v1Message},
      );
      
      if (response.statusCode == 200) {
        print('✅ FCM HTTP v1 request successful!');
        print('📋 Response: ${response.data}');
        print('📱 Notification sent to topic: ${v1Message['topic']}');
      } else {
        print('❌ FCM HTTP v1 request failed: ${response.statusCode}');
        print('📋 Response: ${response.data}');
      }
    } catch (e) {
      print('❌ Error sending FCM HTTP v1 request: $e');
      if (e is DioException) {
        print('📋 Dio error details: ${e.response?.data}');
        print('📋 Status code: ${e.response?.statusCode}');
      }
      // Fallback: Still save to Firestore for app-level notifications
      print('📝 Notification will still be delivered via Firestore listener');
    }
  }
  
  /// Get OAuth 2.0 access token for FCM
  static Future<String?> _getAccessToken() async {
    try {
      print('🔑 Loading service account from file...');
      
      // Load service account credentials directly from the JSON file
      final serviceAccountFile = File('/Users/<USER>/Documents/wallpaper/firefly-8cbce-firebase-adminsdk-fbsvc-c22fa82291.json');
      
      if (!await serviceAccountFile.exists()) {
        print('❌ Service account file not found');
        return null;
      }
      
      final serviceAccountJson = await serviceAccountFile.readAsString();
      print('✅ Service account file loaded successfully');
      
      print('🔐 Creating service account credentials from file...');
      final serviceAccountCredentials = ServiceAccountCredentials.fromJson(serviceAccountJson);
      print('✅ Service account credentials created successfully');
      
      print('🌐 Requesting OAuth client...');
      final client = await clientViaServiceAccount(
        serviceAccountCredentials,
        FirebaseConfig.fcmScopes,
      );
      print('✅ OAuth client obtained successfully');
      
      final accessToken = client.credentials.accessToken.data;
      client.close();
      
      print('✅ OAuth access token obtained successfully');
      print('🔑 Token length: ${accessToken.length}');
      return accessToken;
    } catch (e, stackTrace) {
      print('❌ Error getting OAuth access token: $e');
      print('📍 Stack trace: $stackTrace');
      print('💡 Falling back to hardcoded service account JSON...');
      
      // Fallback to the original method if file loading fails
      try {
        final serviceAccountMap = jsonDecode(FirebaseConfig.serviceAccountKeyJson);
        final serviceAccountCredentials = ServiceAccountCredentials.fromJson(serviceAccountMap);
        
        final client = await clientViaServiceAccount(
          serviceAccountCredentials,
          FirebaseConfig.fcmScopes,
        );
        
        final accessToken = client.credentials.accessToken.data;
        client.close();
        
        print('✅ OAuth access token obtained via fallback method');
        return accessToken;
      } catch (fallbackError) {
        print('❌ Fallback method also failed: $fallbackError');
        return null;
      }
    }
  }
  
  /// Convert legacy FCM message format to HTTP v1 format
  static Map<String, dynamic> _convertToV1Format(Map<String, dynamic> legacyMessage) {
    final v1Message = <String, dynamic>{
      'topic': legacyMessage['to']?.toString().replaceFirst('/topics/', '') ?? 'all_users',
    };
    
    // Add notification payload
    if (legacyMessage['notification'] != null) {
      v1Message['notification'] = {
        'title': legacyMessage['notification']['title'],
        'body': legacyMessage['notification']['body'],
        if (legacyMessage['notification']['image'] != null)
          'image': legacyMessage['notification']['image'],
      };
    }
    
    // Add data payload
    if (legacyMessage['data'] != null) {
      v1Message['data'] = Map<String, String>.from(
        legacyMessage['data'].map((key, value) => MapEntry(key, value.toString()))
      );
    }
    
    // Add Android specific settings
    if (legacyMessage['android'] != null) {
      v1Message['android'] = {
        'notification': {
          'channel_id': legacyMessage['android']['notification']['channel_id'],
          'priority': legacyMessage['android']['priority'],
          'sound': legacyMessage['android']['notification']['sound'],
        },
      };
    }
    
    // Add APNS specific settings
    if (legacyMessage['apns'] != null) {
      v1Message['apns'] = {
        'payload': {
          'aps': {
            'alert': {
              'title': legacyMessage['notification']['title'],
              'body': legacyMessage['notification']['body'],
            },
            'sound': legacyMessage['apns']['payload']['aps']['sound'],
            'badge': legacyMessage['apns']['payload']['aps']['badge'],
          },
        },
      };
    }
    
    return v1Message;
  }



  /// Get notification history
  static Future<List<Map<String, dynamic>>> getNotificationHistory() async {
    try {
      final querySnapshot = await _firestore
          .collection('notification_history')
          .orderBy('timestamp', descending: true)
          .limit(50)
          .get();

      return querySnapshot.docs
          .map((doc) => {'id': doc.id, ...doc.data()})
          .toList();
    } catch (e) {
      print('Error getting notification history: $e');
      return [];
    }
  }

  /// Save notification to history
  static Future<void> _saveNotificationToHistory(
    String title,
    String body,
    String target,
    Map<String, dynamic>? data,
    {String? imageUrl}
  ) async {
    try {
      await _firestore.collection('notification_history').add({
        'title': title,
        'body': body,
        'target': target,
        'data': data ?? {},
        'imageUrl': imageUrl,
        'timestamp': FieldValue.serverTimestamp(),
        'sent_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error saving notification to history: $e');
    }
  }

  /// Subscribe all users to the 'all_users' topic
  static Future<void> subscribeToAllUsersTopic(String fcmToken) async {
    try {
      await FirebaseMessaging.instance.subscribeToTopic('all_users');
      print('Successfully subscribed to all_users topic');
    } catch (e) {
      print('Error subscribing to all_users topic: $e');
    }
  }

  /// Get available notification topics
  static List<String> getAvailableTopics() {
    return [
      'all_users',
      'new_wallpapers',
      'app_updates',
      'featured_content',
      'weekly_highlights',
    ];
  }
}