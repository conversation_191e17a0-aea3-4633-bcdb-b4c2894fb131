import 'package:firebase_core/firebase_core.dart';

class FirebaseConfig {
  static const String cloudinaryCloudName = 'dbdxtlkng';
  static const String cloudinaryApiKey = '***************';
  static const String cloudinaryApiSecret = 'sTJu640xtf30TvCCSmvHrIJPdbM';
  static const String cloudinaryUploadPreset = 'anuved';
  
  // Firebase Realtime Database structure
  static const String wallpapersNode = 'wallpapers';
  static const String categoriesNode = 'categories';
  
  // Admin email
  static const String adminEmail = '<EMAIL>';
  
  // FCM HTTP v1 API Configuration (OAuth 2.0)
  // Service Account Key JSON (Download from Firebase Console > Project Settings > Service Accounts)
  static const String serviceAccountKeyJson = '''
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

''';
  
  // FCM Scope for OAuth 2.0
  static const List<String> fcmScopes = [
    'https://www.googleapis.com/auth/firebase.messaging'
  ];
  
  // Cloudinary folder structure
  static const Map<String, String> categoryFolders = {
    'Nature': 'nature',
    'Abstract': 'abstract',
    'Cars': 'cars',
    'Galaxy': 'galaxy',
    'Architecture': 'architecture',
    'Animals': 'animals',
    'Technology': 'technology',
    'Art': 'art',
    'Minimalist': 'minimalist',
    'Dark': 'dark',
  };
  
  // Image quality settings
  static const String hdQuality = 'q_90,f_auto';
  static const String thumbnailQuality = 'q_70,f_auto,w_400,h_600,c_fill';
  
  // Firebase options (will be auto-configured from google-services.json)
  static FirebaseOptions? _firebaseOptions;
  
  static FirebaseOptions? get firebaseOptions => _firebaseOptions;
  
  static void setFirebaseOptions(FirebaseOptions options) {
    _firebaseOptions = options;
  }
  
  // Helper method to get Cloudinary URL with transformations
  static String getCloudinaryUrl({
    required String publicId,
    String? folder,
    bool isHd = false,
  }) {
    final quality = isHd ? hdQuality : thumbnailQuality;
    final folderPath = folder != null ? '$folder/' : '';
    
    return 'https://res.cloudinary.com/$cloudinaryCloudName/image/upload/$quality/$folderPath$publicId';
  }
  
  // Helper method to get folder name for category
  static String getFolderForCategory(String category) {
    // Check if predefined category exists
    if (categoryFolders.containsKey(category)) {
      return categoryFolders[category]!;
    }
    
    // For new categories, create a clean folder name
    return category
        .toLowerCase()
        .replaceAll(' ', '_')
        .replaceAll(RegExp(r'[^a-z0-9_]'), '') // Remove special characters
        .replaceAll(RegExp(r'_+'), '_') // Replace multiple underscores with single
        .replaceAll(RegExp(r'^_|_$'), ''); // Remove leading/trailing underscores
  }
  
  // Validation methods
  static bool isValidImageFormat(String fileName) {
    final validExtensions = ['.jpg', '.jpeg', '.png', '.webp'];
    final lowerFileName = fileName.toLowerCase();
    return validExtensions.any((ext) => lowerFileName.endsWith(ext));
  }
  
  static bool isAdminUser(String? email) {
    return email?.toLowerCase() == adminEmail.toLowerCase();
  }
}

// Firebase initialization helper
class FirebaseInitializer {
  static Future<void> initialize() async {
    try {
      await Firebase.initializeApp();
      print('Firebase initialized successfully');
    } catch (e) {
      print('Firebase initialization error: $e');
      rethrow;
    }
  }
}