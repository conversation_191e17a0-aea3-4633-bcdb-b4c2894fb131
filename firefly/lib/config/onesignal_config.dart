class OneSignalConfig {
  // OneSignal App ID - Replace with your actual OneSignal App ID
  static const String appId = '************************************';
  
  // OneSignal REST API Key - Replace with your actual REST API Key
  static const String restApiKey = 'os_v2_app_onho4uhcsrannhnv3oqlvv7lg2oimtdlsfteva4emzswwpibih6y7wqmkxqbm4mfikuod2zem5bfocm7w4ymcc2xp6w5hlvd3tb36ki';
  
  // Notification channel settings
  static const String channelId = 'firefly_onesignal_notifications';
  static const String channelName = 'FireFly Notifications';
  static const String channelDescription = 'Push notifications for new wallpapers and updates';
  
  // Notification categories/types
  static const String newWallpaperType = 'new_wallpaper';
  static const String categoryUpdateType = 'category_update';
  static const String appUpdateType = 'app_update';
  static const String generalType = 'general';
  
  // User segments
  static const String allUsersSegment = 'All';
  static const String androidUsersSegment = 'Android Users';
  static const String iosUsersSegment = 'iOS Users';
  static const String activeUsersSegment = 'Active Users';
  
  // Tags for user segmentation
  static const String platformTag = 'platform';
  static const String appVersionTag = 'app_version';
  static const String userTypeTag = 'user_type';
  static const String languageTag = 'language';
  static const String notificationsEnabledTag = 'notifications_enabled';
  static const String favoriteCategories = 'favorite_categories';
  
  // Deep link schemes
  static const String deepLinkScheme = 'firefly';
  static const String wallpaperDeepLink = '$deepLinkScheme://wallpaper';
  static const String categoryDeepLink = '$deepLinkScheme://category';
  static const String homeDeepLink = '$deepLinkScheme://home';
  
  // Notification templates
  static Map<String, String> get newWallpaperTemplate => {
    'title': '🔥 New Wallpaper Added!',
    'message': 'Check out the latest {{category}} wallpaper in FireFly',
    'type': newWallpaperType,
  };
  
  static Map<String, String> get categoryUpdateTemplate => {
    'title': '📂 {{category}} Updated!',
    'message': 'New wallpapers added to {{category}} category',
    'type': categoryUpdateType,
  };
  
  static Map<String, String> get appUpdateTemplate => {
    'title': '🚀 FireFly Updated!',
    'message': 'New features and improvements are available',
    'type': appUpdateType,
  };
  
  // Validation methods
  static bool get isConfigured => appId != 'YOUR_ONESIGNAL_APP_ID';
  
  static bool get hasRestApiKey => restApiKey != 'YOUR_ONESIGNAL_REST_API_KEY';
  
  static void validateConfiguration() {
    if (!isConfigured) {
      throw Exception('OneSignal App ID not configured. Please update OneSignalConfig.appId');
    }
    
    if (!hasRestApiKey) {
      print('⚠️ OneSignal REST API Key not configured. Some features may not work.');
    }
  }
}
