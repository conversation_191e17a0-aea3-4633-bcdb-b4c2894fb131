import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import '../config/firebase_config.dart';

class AdminConfigProvider with ChangeNotifier {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  
  List<String> _adminEmails = [FirebaseConfig.adminEmail]; // Default admin
  bool _isLoading = false;
  String? _errorMessage;
  
  List<String> get adminEmails => _adminEmails;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  
  AdminConfigProvider() {
    _loadAdminEmails();
  }
  
  // Check if an email is admin
  bool isAdminUser(String? email) {
    if (email == null) return false;
    return _adminEmails.any((adminEmail) => 
        adminEmail.toLowerCase() == email.toLowerCase());
  }
  
  // Load admin emails from Firestore
  Future<void> _loadAdminEmails() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final doc = await _firestore.collection('config').doc('admin').get();
      
      if (doc.exists) {
        final data = doc.data();
        if (data != null && data['emails'] != null) {
          _adminEmails = List<String>.from(data['emails']);
          // Sync to Realtime Database
          await _syncAdminEmailsToRealtimeDB();
        }
      } else {
        // Create default admin config if it doesn't exist
        await _createDefaultAdminConfig();
      }
    } catch (e) {
      _errorMessage = 'Failed to load admin configuration: ${e.toString()}';
      // Keep default admin email if loading fails
      _adminEmails = [FirebaseConfig.adminEmail];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Sync admin emails to Realtime Database for rules
  Future<void> _syncAdminEmailsToRealtimeDB() async {
    try {
      final adminEmailsMap = <String, bool>{};
      for (final email in _adminEmails) {
        // Replace dots with commas for Firebase key compatibility
        final key = email.replaceAll('.', ',');
        adminEmailsMap[key] = true;
      }
      await _database.ref('admin_emails').set(adminEmailsMap);
    } catch (e) {
      if (kDebugMode) {
        print('Failed to sync admin emails to Realtime DB: ${e.toString()}');
      }
    }
  }

  // Create default admin configuration in Firestore
  Future<void> _createDefaultAdminConfig() async {
    try {
      await _firestore.collection('config').doc('admin').set({
        'emails': [FirebaseConfig.adminEmail],
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      _adminEmails = [FirebaseConfig.adminEmail];
      // Sync to Realtime Database
      await _syncAdminEmailsToRealtimeDB();
    } catch (e) {
      throw Exception('Failed to create default admin config: ${e.toString()}');
    }
  }
  
  // Add a new admin email (only existing admins can do this)
  Future<bool> addAdminEmail({
    required String newAdminEmail,
    required String currentUserEmail,
  }) async {
    try {
      // Check if current user is admin
      if (!isAdminUser(currentUserEmail)) {
        _errorMessage = 'Unauthorized: Only admins can add new admins';
        return false;
      }
      
      // Check if email is already admin
      if (isAdminUser(newAdminEmail)) {
        _errorMessage = 'Email is already an admin';
        return false;
      }
      
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final updatedEmails = [..._adminEmails, newAdminEmail.toLowerCase()];
      
      await _firestore.collection('config').doc('admin').update({
        'emails': updatedEmails,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      _adminEmails = updatedEmails;
      // Sync to Realtime Database
      await _syncAdminEmailsToRealtimeDB();
      return true;
    } catch (e) {
      _errorMessage = 'Failed to add admin: ${e.toString()}';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Remove an admin email (only existing admins can do this)
  Future<bool> removeAdminEmail({
    required String adminEmailToRemove,
    required String currentUserEmail,
  }) async {
    try {
      // Check if current user is admin
      if (!isAdminUser(currentUserEmail)) {
        _errorMessage = 'Unauthorized: Only admins can remove admins';
        return false;
      }
      
      // Prevent removing the last admin
      if (_adminEmails.length <= 1) {
        _errorMessage = 'Cannot remove the last admin';
        return false;
      }
      
      // Prevent self-removal
      if (currentUserEmail.toLowerCase() == adminEmailToRemove.toLowerCase()) {
        _errorMessage = 'Cannot remove yourself as admin';
        return false;
      }
      
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final updatedEmails = _adminEmails
          .where((email) => email.toLowerCase() != adminEmailToRemove.toLowerCase())
          .toList();
      
      await _firestore.collection('config').doc('admin').update({
        'emails': updatedEmails,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      _adminEmails = updatedEmails;
      // Sync to Realtime Database
      await _syncAdminEmailsToRealtimeDB();
      return true;
    } catch (e) {
      _errorMessage = 'Failed to remove admin: ${e.toString()}';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Refresh admin emails from Firestore
  Future<void> refreshAdminEmails() async {
    await _loadAdminEmails();
  }
  
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}