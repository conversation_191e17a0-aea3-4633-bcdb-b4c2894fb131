import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'admin_config_provider.dart';
import '../services/notification_service.dart';

class AuthProvider with ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final AdminConfigProvider _adminConfigProvider;
  
  User? _user;
  bool _isLoading = true; // Start with loading true to wait for initial auth state
  bool _isInitialized = false;
  String? _errorMessage;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isLoggedIn => _user != null;
  bool get isAdmin => _adminConfigProvider.isAdminUser(_user?.email);

  AuthProvider(this._adminConfigProvider) {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    _auth.authStateChanges().listen((User? user) async {
      print('🔍 AUTH STATE CHANGE: User = ${user?.email ?? "null"}, isLoading = $_isLoading, isInitialized = $_isInitialized');
      _user = user;
      
      // Subscribe to notifications when user logs in
      if (user != null) {
        try {
          await NotificationService.subscribeToTopic('all_users');
          print('✅ User logged in - subscribed to notifications');
        } catch (e) {
          print('⚠️ Failed to subscribe to notifications: $e');
        }
      }
      
      if (!_isInitialized) {
        _isInitialized = true;
        _isLoading = false;
        print('🔍 AUTH INITIALIZED: isLoading = $_isLoading, user = ${user?.email ?? "null"}');
      } else {
        // If already initialized, ensure loading is false after auth state change
        _isLoading = false;
      }
      // Always notify listeners when auth state changes
      print('🔍 NOTIFYING LISTENERS: isLoggedIn = ${user != null}, isLoading = $_isLoading');
      notifyListeners();
    });
    
    // Set a timeout to ensure we don't wait forever
    Future.delayed(const Duration(seconds: 3), () {
      if (!_isInitialized) {
        _isInitialized = true;
        _isLoading = false;
        notifyListeners();
      }
    });
  }

  Future<bool> signInWithGoogle() async {
    try {
      _setLoading(true);
      _clearError();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _setLoading(false);
        return false;
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final UserCredential userCredential = await _auth.signInWithCredential(credential);
      _user = userCredential.user;
      // Don't set loading to false here - let the auth state change listener handle it
      // This prevents race condition where UI shows login screen briefly after successful login
      return true;
    } catch (e) {
      _setError('Failed to sign in with Google: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _googleSignIn.signOut();
      await _auth.signOut();
      _user = null;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to sign out: ${e.toString()}');
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}