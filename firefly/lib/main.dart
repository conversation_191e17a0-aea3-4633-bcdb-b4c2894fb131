import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'screens/splash_screen.dart';
import 'screens/onboarding_screen.dart';
import 'screens/home_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/wallpaper_provider.dart';
import 'providers/admin_config_provider.dart';
import 'utils/app_theme.dart';
import 'services/notification_listener_service.dart';
import 'services/fcm_debug_service.dart';

// Import the notification service
import 'services/notification_service.dart';
// Import the background handler
import 'services/firebase_background_handler.dart';
// Import analytics service
import 'services/analytics_service.dart';
// Import battery optimization service
import 'services/battery_optimization_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services first
  try {
    await Firebase.initializeApp();
    print('✅ Firebase initialized');
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
  }

  // Run app immediately to avoid white screen
  runApp(const MyApp());
}

// Initialize additional services after app starts
Future<void> initializeServices() async {
  try {
    print('🚀 Starting additional service initialization...');

    // Mobile Ads SDK removed - no longer needed
    // Ad Manager removed - no longer needed

    // Set up background message handler
    try {
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      print('✅ Background message handler registered');
    } catch (e) {
      print('❌ Error registering background message handler: $e');
    }

    // Initialize analytics service
    try {
      await AnalyticsService.initialize();
      print('✅ Analytics service initialized');
    } catch (e) {
      print('❌ Analytics service failed: $e');
    }

    // Initialize notification service
    try {
      await NotificationService.initialize();
      print('✅ Notification service initialized');

      // Check and request background notification permissions
      bool hasBackgroundPerms =
          await NotificationService.hasBackgroundNotificationPermissions();
      if (!hasBackgroundPerms) {
        print('⚠️ Missing background notification permissions');
        print('🔔 Requesting background notification permissions...');
        await NotificationService.requestNotificationPermission();
      } else {
        print('✅ Background notification permissions already granted');
      }
    } catch (e) {
      print('⚠️ Notification service failed: $e');
    }

    // Initialize notification listener service with delay
    try {
      // Add delay to ensure Firebase Auth is ready
      await Future.delayed(const Duration(seconds: 2));
      await NotificationListenerService.initialize();
      print('✅ Notification listener service initialized');
    } catch (e) {
      print('⚠️ Notification listener service failed (non-critical): $e');
    }

    // Run FCM diagnostics in debug mode
    if (kDebugMode) {
      print('🔍 Running FCM diagnostics in debug mode...');
      try {
        await FCMDebugService.runComprehensiveDiagnostics();
      } catch (e) {
        print('⚠️ FCM diagnostics failed: $e');
      }
    }

    print('🎉 All services initialized successfully');
  } catch (e, stackTrace) {
    print('❌ Service initialization error: $e');
    print('Stack trace: $stackTrace');
    // Continue app execution even if some services fail
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AdminConfigProvider()),
        ChangeNotifierProxyProvider<AdminConfigProvider, AuthProvider>(
          create: (context) =>
              AuthProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) =>
              previous ?? AuthProvider(adminConfig),
        ),
        ChangeNotifierProxyProvider<AdminConfigProvider, WallpaperProvider>(
          create: (context) =>
              WallpaperProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) =>
              previous ?? WallpaperProvider(adminConfig),
        ),
      ],
      child: MaterialApp(
        title: 'FireFly - 4K HD Wallpaper',
        theme: AppTheme.lightTheme,
        home: const AuthWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _showOnboarding = true;
  bool _isCheckingOnboarding = true;

  @override
  void initState() {
    super.initState();
    _checkOnboardingStatus();
  }

  // Check if user has completed onboarding before
  Future<void> _checkOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasCompletedOnboarding =
          prefs.getBool('has_completed_onboarding') ?? false;

      if (mounted) {
        setState(() {
          _showOnboarding = !hasCompletedOnboarding;
          _isCheckingOnboarding = false;
        });
      }
    } catch (e) {
      print('❌ Error checking onboarding status: $e');
      if (mounted) {
        setState(() {
          _showOnboarding = true; // Default to showing onboarding on error
          _isCheckingOnboarding = false;
        });
      }
    }
  }

  // This will be called when user clicks "Get Started"
  Future<void> _initializeAppServices() async {
    try {
      // Add a small delay to ensure Firebase is ready
      await Future.delayed(const Duration(milliseconds: 500));
      await initializeServices();

      // Track app open event
      try {
        await AnalyticsService.trackAppOpen();
      } catch (e) {
        print('❌ Failed to track app open: $e');
      }

      // Check battery optimization after a delay (don't block app startup)
      Future.delayed(const Duration(seconds: 3), () async {
        if (mounted) {
          try {
            bool isBatteryOptimized =
                !(await BatteryOptimizationService.isBatteryOptimizationDisabled());
            if (isBatteryOptimized) {
              print(
                '⚠️ Battery optimization is enabled - notifications may be unreliable',
              );
              // Show dialog after another delay to not interrupt user experience
              Future.delayed(const Duration(seconds: 5), () {
                if (mounted) {
                  BatteryOptimizationService.showBatteryOptimizationDialog(
                    context,
                  );
                }
              });
            }
          } catch (e) {
            print('❌ Error checking battery optimization: $e');
          }
        }
      });
    } catch (e) {
      print('Failed to initialize services: $e');
    }
  }

  Future<void> _onGetStarted() async {
    // Save onboarding completion status
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_onboarding', true);
      print('✅ Onboarding completion saved to SharedPreferences');
    } catch (e) {
      print('❌ Error saving onboarding status: $e');
    }

    setState(() {
      _showOnboarding = false;
    });
    // Initialize services in background after user proceeds
    _initializeAppServices();
  }

  @override
  Widget build(BuildContext context) {
    // Show splash while checking onboarding status
    if (_isCheckingOnboarding) {
      return const SplashScreen();
    }

    // Show onboarding screen if user hasn't completed it
    if (_showOnboarding) {
      return OnboardingScreen(onGetStarted: _onGetStarted);
    }

    // After onboarding, show splash while services initialize, then home
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        print(
          '🔍🔍🔍 AUTH WRAPPER REBUILD: isLoading = ${authProvider.isLoading}, isLoggedIn = ${authProvider.isLoggedIn}, isAnonymous = ${authProvider.isAnonymous}, user = ${authProvider.user?.uid ?? "null"}',
        );

        // Show splash screen while loading or during initial auth state check
        if (authProvider.isLoading) {
          print('🔍 SHOWING: SplashScreen (loading)');
          return const SplashScreen();
        }

        // After services are initialized, go to home screen
        print('🔍 SHOWING: HomeScreen');
        return const HomeScreen();
      },
    );
  }
}
