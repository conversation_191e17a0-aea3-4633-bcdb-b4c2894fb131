import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart';
import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'screens/login_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/wallpaper_provider.dart';
import 'providers/admin_config_provider.dart';
import 'utils/app_theme.dart';
import 'services/notification_listener_service.dart';
import 'services/fcm_debug_service.dart';


// Import the notification service
import 'services/notification_service.dart';
// Import the background handler
import 'services/firebase_background_handler.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize core services first
  try {
    await Firebase.initializeApp();
    print('✅ Firebase initialized');
  } catch (e) {
    print('❌ Firebase initialization failed: $e');
  }
  
  // Run app immediately to avoid white screen
  runApp(const MyApp());
}

// Initialize additional services after app starts
Future<void> initializeServices() async {
  try {
    print('🚀 Starting additional service initialization...');
    
    // Mobile Ads SDK removed - no longer needed
    // Ad Manager removed - no longer needed
    
    // Set up background message handler
    try {
      FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
      print('✅ Background message handler registered');
    } catch (e) {
      print('❌ Error registering background message handler: $e');
    }
    
    // Initialize notification service
    try {
      await NotificationService.initialize();
      print('✅ Notification service initialized');
      
      // Check and request background notification permissions
      bool hasBackgroundPerms = await NotificationService.hasBackgroundNotificationPermissions();
      if (!hasBackgroundPerms) {
        print('⚠️ Missing background notification permissions');
        print('🔔 Requesting background notification permissions...');
        await NotificationService.requestNotificationPermission();
      } else {
        print('✅ Background notification permissions already granted');
      }
    } catch (e) {
      print('⚠️ Notification service failed: $e');
    }
    
    // Initialize notification listener service with delay
    try {
      // Add delay to ensure Firebase Auth is ready
      await Future.delayed(const Duration(seconds: 2));
      await NotificationListenerService.initialize();
      print('✅ Notification listener service initialized');
    } catch (e) {
      print('⚠️ Notification listener service failed (non-critical): $e');
    }
    
    // Run FCM diagnostics in debug mode
    if (kDebugMode) {
      print('🔍 Running FCM diagnostics in debug mode...');
      try {
        await FCMDebugService.runComprehensiveDiagnostics();
      } catch (e) {
        print('⚠️ FCM diagnostics failed: $e');
      }
    }
    
    print('🎉 All services initialized successfully');
  } catch (e, stackTrace) {
    print('❌ Service initialization error: $e');
    print('Stack trace: $stackTrace');
    // Continue app execution even if some services fail
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AdminConfigProvider()),
        ChangeNotifierProxyProvider<AdminConfigProvider, AuthProvider>(
          create: (context) => AuthProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) => 
              previous ?? AuthProvider(adminConfig),
        ),
        ChangeNotifierProxyProvider<AdminConfigProvider, WallpaperProvider>(
          create: (context) => WallpaperProvider(context.read<AdminConfigProvider>()),
          update: (context, adminConfig, previous) => 
              previous ?? WallpaperProvider(adminConfig),
        ),
      ],
      child: MaterialApp(
        title: 'FireFly - 4K HD Wallpaper',
        theme: AppTheme.lightTheme,
        home: const AuthWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _servicesInitialized = false;
  bool _initializationFailed = false;

  @override
  void initState() {
    super.initState();
    _initializeAppServices();
  }

  Future<void> _initializeAppServices() async {
    try {
      // Add a small delay to ensure Firebase is ready
      await Future.delayed(const Duration(milliseconds: 500));
      await initializeServices();
      if (mounted) {
        setState(() {
          _servicesInitialized = true;
        });
      }
    } catch (e) {
      print('Failed to initialize services: $e');
      if (mounted) {
        setState(() {
          _initializationFailed = true;
          _servicesInitialized = true; // Continue anyway
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show splash screen while services are initializing
    if (!_servicesInitialized) {
      return const SplashScreen();
    }

    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        print('🔍🔍🔍 AUTH WRAPPER REBUILD: isLoading = ${authProvider.isLoading}, isLoggedIn = ${authProvider.isLoggedIn}, user = ${authProvider.user?.email ?? "null"}');
        
        // Show splash screen while loading or during initial auth state check
        if (authProvider.isLoading) {
          print('🔍 SHOWING: SplashScreen (loading)');
          return const SplashScreen();
        }
        
        // If user is logged in, go to home screen
        if (authProvider.isLoggedIn) {
          print('🔍 SHOWING: HomeScreen (logged in)');
          return const HomeScreen();
        }
        
        // If no user and not loading, show login screen
        print('🔍 SHOWING: LoginScreen (not logged in)');
        return const LoginScreen();
      },
    );
  }
}
