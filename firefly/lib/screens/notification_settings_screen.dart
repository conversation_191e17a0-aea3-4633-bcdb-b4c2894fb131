import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/notification_service.dart';
import '../services/battery_optimization_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  bool _notificationsEnabled = true;
  bool _newWallpaperNotifications = true;
  bool _updateNotifications = true;
  bool _promotionalNotifications = false;
  bool _isBatteryOptimized = true;
  bool _canScheduleAlarms = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load notification preferences
      _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
      _newWallpaperNotifications =
          prefs.getBool('new_wallpaper_notifications') ?? true;
      _updateNotifications = prefs.getBool('update_notifications') ?? true;
      _promotionalNotifications =
          prefs.getBool('promotional_notifications') ?? false;

      // Check system settings
      _isBatteryOptimized =
          !(await BatteryOptimizationService.isBatteryOptimizationDisabled());
      _canScheduleAlarms =
          await BatteryOptimizationService.canScheduleExactAlarms();

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading notification settings: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notifications_enabled', _notificationsEnabled);
      await prefs.setBool(
        'new_wallpaper_notifications',
        _newWallpaperNotifications,
      );
      await prefs.setBool('update_notifications', _updateNotifications);
      await prefs.setBool(
        'promotional_notifications',
        _promotionalNotifications,
      );

      // Update FCM topic subscriptions based on preferences
      if (_notificationsEnabled) {
        await NotificationService.subscribeToTopic('all_users');

        if (_newWallpaperNotifications) {
          await NotificationService.subscribeToTopic('new_wallpapers');
        } else {
          await NotificationService.unsubscribeFromTopic('new_wallpapers');
        }

        if (_updateNotifications) {
          await NotificationService.subscribeToTopic('app_updates');
        } else {
          await NotificationService.unsubscribeFromTopic('app_updates');
        }

        if (_promotionalNotifications) {
          await NotificationService.subscribeToTopic('promotions');
        } else {
          await NotificationService.unsubscribeFromTopic('promotions');
        }
      } else {
        // Unsubscribe from all topics if notifications are disabled
        await NotificationService.unsubscribeFromTopic('all_users');
        await NotificationService.unsubscribeFromTopic('new_wallpapers');
        await NotificationService.unsubscribeFromTopic('app_updates');
        await NotificationService.unsubscribeFromTopic('promotions');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification settings saved!'),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      print('Error saving notification settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0a0a0a),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          'Notification Settings',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
            fontFamily: 'Poppins',
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: Colors.white),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667eea)),
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // System Settings Section
                  _buildSectionHeader('System Settings'),
                  const SizedBox(height: 16),

                  if (_isBatteryOptimized) ...[
                    _buildSystemSettingCard(
                      icon: Icons.battery_saver,
                      title: 'Battery Optimization',
                      subtitle: 'Disable to receive notifications reliably',
                      status: 'Needs Attention',
                      statusColor: Colors.orange,
                      onTap: () async {
                        await BatteryOptimizationService.showBatteryOptimizationDialog(
                          context,
                        );
                        await _loadSettings(); // Refresh after user returns
                      },
                    ),
                    const SizedBox(height: 12),
                  ],

                  if (!_canScheduleAlarms) ...[
                    _buildSystemSettingCard(
                      icon: Icons.schedule,
                      title: 'Exact Alarms',
                      subtitle: 'Required for scheduled notifications',
                      status: 'Needs Permission',
                      statusColor: Colors.red,
                      onTap: () async {
                        await BatteryOptimizationService.requestExactAlarmPermission();
                        await _loadSettings(); // Refresh after user returns
                      },
                    ),
                    const SizedBox(height: 12),
                  ],

                  _buildSystemSettingCard(
                    icon: Icons.settings,
                    title: 'App Notification Settings',
                    subtitle: 'Manage system notification permissions',
                    status: 'Configure',
                    statusColor: Colors.blue,
                    onTap: () async {
                      await BatteryOptimizationService.showNotificationSettingsGuide(
                        context,
                      );
                    },
                  ),

                  const SizedBox(height: 32),

                  // Notification Preferences Section
                  _buildSectionHeader('Notification Preferences'),
                  const SizedBox(height: 16),

                  _buildSwitchTile(
                    title: 'Enable Notifications',
                    subtitle: 'Receive all app notifications',
                    value: _notificationsEnabled,
                    onChanged: (value) {
                      setState(() {
                        _notificationsEnabled = value;
                        if (!value) {
                          // Disable all other notifications if main toggle is off
                          _newWallpaperNotifications = false;
                          _updateNotifications = false;
                          _promotionalNotifications = false;
                        }
                      });
                    },
                  ),

                  const SizedBox(height: 16),

                  AnimatedOpacity(
                    opacity: _notificationsEnabled ? 1.0 : 0.5,
                    duration: const Duration(milliseconds: 300),
                    child: Column(
                      children: [
                        _buildSwitchTile(
                          title: 'New Wallpapers',
                          subtitle:
                              'Get notified when new wallpapers are added',
                          value: _newWallpaperNotifications,
                          onChanged: _notificationsEnabled
                              ? (value) {
                                  setState(() {
                                    _newWallpaperNotifications = value;
                                  });
                                }
                              : null,
                        ),

                        const SizedBox(height: 16),

                        _buildSwitchTile(
                          title: 'App Updates',
                          subtitle:
                              'Notifications about app updates and new features',
                          value: _updateNotifications,
                          onChanged: _notificationsEnabled
                              ? (value) {
                                  setState(() {
                                    _updateNotifications = value;
                                  });
                                }
                              : null,
                        ),

                        const SizedBox(height: 16),

                        _buildSwitchTile(
                          title: 'Promotional',
                          subtitle: 'Special offers and promotional content',
                          value: _promotionalNotifications,
                          onChanged: _notificationsEnabled
                              ? (value) {
                                  setState(() {
                                    _promotionalNotifications = value;
                                  });
                                }
                              : null,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Actions Section
                  _buildSectionHeader('Actions'),
                  const SizedBox(height: 16),

                  _buildActionButton(
                    icon: Icons.refresh,
                    title: 'Refresh Notification Token',
                    subtitle: 'Fix notification delivery issues',
                    onTap: () async {
                      try {
                        await NotificationService.refreshTokenPeriodically();
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Notification token refreshed!'),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error refreshing token: $e'),
                              backgroundColor: Colors.red,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  _buildActionButton(
                    icon: Icons.restart_alt,
                    title: 'Reset Onboarding',
                    subtitle: 'Show the Get Started screen again',
                    onTap: () async {
                      try {
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('has_completed_onboarding', false);
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Onboarding reset! Restart the app to see Get Started screen.',
                              ),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error resetting onboarding: $e'),
                              backgroundColor: Colors.red,
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      }
                    },
                  ),

                  const SizedBox(height: 12),

                  _buildActionButton(
                    icon: Icons.notifications_active,
                    title: 'Test Notification',
                    subtitle: 'Send a test notification to verify setup',
                    onTap: () async {
                      await NotificationService.showLocalNotification(
                        title: 'Test Notification',
                        body:
                            'If you see this, notifications are working correctly!',
                        payload: '{"type": "test"}',
                      );
                    },
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
        fontFamily: 'Poppins',
      ),
    );
  }

  Widget _buildSystemSettingCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required String status,
    required Color statusColor,
    required VoidCallback onTap,
  }) {
    return Card(
      color: Colors.grey[900],
      child: ListTile(
        leading: Icon(icon, color: const Color(0xFF667eea)),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(subtitle, style: const TextStyle(color: Colors.white70)),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: statusColor),
          ),
          child: Text(
            status,
            style: TextStyle(
              color: statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Card(
      color: Colors.grey[900],
      child: SwitchListTile(
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(subtitle, style: const TextStyle(color: Colors.white70)),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF667eea),
        inactiveThumbColor: Colors.grey,
        inactiveTrackColor: Colors.grey[700],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      color: Colors.grey[900],
      child: ListTile(
        leading: Icon(icon, color: const Color(0xFF667eea)),
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(subtitle, style: const TextStyle(color: Colors.white70)),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: Colors.white54,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }
}
