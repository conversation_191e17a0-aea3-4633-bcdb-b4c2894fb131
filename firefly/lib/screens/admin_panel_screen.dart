import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/wallpaper_provider.dart';
import '../utils/app_theme.dart';
import '../services/admin_notification_service.dart';

class AdminPanelScreen extends StatefulWidget {
  const AdminPanelScreen({super.key});

  @override
  State<AdminPanelScreen> createState() => _AdminPanelScreenState();
}

class _AdminPanelScreenState extends State<AdminPanelScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _categoryController = TextEditingController();
  final _notificationTitleController = TextEditingController();
  final _notificationBodyController = TextEditingController();
  final _notificationImageUrlController = TextEditingController();

  final ImagePicker _picker = ImagePicker();

  List<XFile> _selectedImages = [];
  String _selectedCategory = 'Nature';
  bool _isNewCategory = false;
  String _selectedNotificationTarget = 'all_users';
  bool _isSendingNotification = false;
  bool _isPremiumWallpaper = false;

  final List<String> _predefinedCategories = [
    'Nature',
    'Abstract',
    'Cars',
    'Galaxy',
    'Architecture',
    'Animals',
    'Technology',
    'Art',
    'Minimalist',
    'Dark',
  ];

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    // Load wallpapers when admin panel opens to get latest categories
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final wallpaperProvider = Provider.of<WallpaperProvider>(
        context,
        listen: false,
      );
      if (wallpaperProvider.categories.isEmpty) {
        wallpaperProvider.loadWallpapers();
      }
    });
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _categoryController.dispose();
    _notificationTitleController.dispose();
    _notificationBodyController.dispose();
    _notificationImageUrlController.dispose();

    _animationController.dispose();
    super.dispose();
  }

  Future<void> _pickImages({bool multiple = false}) async {
    try {
      if (multiple) {
        final images = await _picker.pickMultiImage(imageQuality: 100);
        setState(() {
          _selectedImages = images;
        });
      } else {
        final image = await _picker.pickImage(
          source: ImageSource.gallery,
          imageQuality: 100,
        );
        if (image != null) {
          setState(() {
            _selectedImages = [image];
          });
        }
      }
    } catch (e) {
      _showSnackBar('Failed to pick images: ${e.toString()}', isError: true);
    }
  }

  Future<void> _uploadWallpapers() async {
    if (_selectedImages.isEmpty) {
      _showSnackBar('Please select at least one image', isError: true);
      return;
    }

    if (_isNewCategory && _categoryController.text.trim().isEmpty) {
      _showSnackBar('Please enter a category name', isError: true);
      return;
    }

    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    final category = _isNewCategory
        ? _categoryController.text.trim()
        : _selectedCategory;

    bool success;
    if (_selectedImages.length == 1) {
      final title = _titleController.text.trim().isEmpty
          ? 'Wallpaper ${DateTime.now().millisecondsSinceEpoch}'
          : _titleController.text.trim();

      success = await wallpaperProvider.uploadWallpaper(
        imagePath: _selectedImages.first.path,
        title: title,
        category: category,
        isPremium: _isPremiumWallpaper,
      );

      // Send notification about new wallpaper
      if (success) {
        await AdminNotificationService.sendNewWallpaperNotification(
          wallpaperTitle: title,
          category: category,
        );
      }
    } else {
      success = await wallpaperProvider.uploadMultipleWallpapers(
        imagePaths: _selectedImages.map((img) => img.path).toList(),
        category: category,
        isPremium: _isPremiumWallpaper,
      );

      // Send notification about new wallpapers
      if (success) {
        await AdminNotificationService.sendNotificationToAll(
          title: 'New Wallpapers Added! 🎨',
          body:
              '${_selectedImages.length} new wallpapers added to $category category',
          data: {'screen': 'category', 'category_name': category},
        );
      }
    }

    if (success) {
      _showSnackBar('Wallpapers uploaded successfully!');

      // Refresh to update categories list (real-time listener will handle this automatically)
      // await wallpaperProvider.loadWallpapers();

      _resetForm();
    } else {
      _showSnackBar(
        wallpaperProvider.errorMessage ?? 'Failed to upload wallpapers',
        isError: true,
      );
    }
  }

  void _resetForm() {
    setState(() {
      _selectedImages.clear();
      _titleController.clear();
      _categoryController.clear();
      _selectedCategory = 'Nature';
      _isNewCategory = false;
      _isPremiumWallpaper = false;
    });

    // Refresh wallpapers to update categories dropdown
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    wallpaperProvider.loadWallpapers().then((_) {
      if (mounted) {
        setState(() {}); // Refresh UI to show new categories
      }
    });
  }

  Future<void> _sendNotification() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSendingNotification = true;
    });

    try {
      bool success;

      String? imageUrl = _notificationImageUrlController.text.trim().isEmpty
          ? null
          : _notificationImageUrlController.text.trim();

      if (_selectedNotificationTarget == 'all_users') {
        success = await AdminNotificationService.sendNotificationToAll(
          title: _notificationTitleController.text.trim(),
          body: _notificationBodyController.text.trim(),
          imageUrl: imageUrl,
        );
      } else {
        success = await AdminNotificationService.sendNotificationToTopic(
          topic: _selectedNotificationTarget,
          title: _notificationTitleController.text.trim(),
          body: _notificationBodyController.text.trim(),
          imageUrl: imageUrl,
        );
      }

      if (success) {
        _showSnackBar('Notification sent successfully!');
        _notificationTitleController.clear();
        _notificationBodyController.clear();
        _notificationImageUrlController.clear();
      } else {
        _showSnackBar('Failed to send notification', isError: true);
      }
    } catch (e) {
      _showSnackBar(
        'Error sending notification: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() {
        _isSendingNotification = false;
      });
    }
  }

  void _sendTestNotification() async {
    setState(() {
      _isSendingNotification = true;
    });

    try {
      bool success = await AdminNotificationService.sendNotificationToAll(
        title: 'Test Notification 🧪',
        body:
            'यह एक टेस्ट नोटिफिकेशन है। अगर आपको यह दिख रहा है तो Firebase notifications सही तरीके से काम कर रहे हैं!',
        data: {
          'screen': 'home',
          'test': 'true',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );

      if (success) {
        _showSnackBar(
          'Test notification sent successfully! Check your device.',
        );
      } else {
        _showSnackBar('Failed to send test notification', isError: true);
      }
    } catch (e) {
      _showSnackBar(
        'Error sending test notification: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() {
        _isSendingNotification = false;
      });
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Panel'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.admin_panel_settings,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Upload Wallpapers',
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Add new wallpapers to the collection',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Image Selection
                Text(
                  'Select Images',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _pickImages(multiple: false),
                        icon: const Icon(Icons.image),
                        label: const Text('Single Image'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryBlue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _pickImages(multiple: true),
                        icon: const Icon(Icons.photo_library),
                        label: const Text('Multiple Images'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryGreen,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Selected Images Preview
                if (_selectedImages.isNotEmpty) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Selected Images (${_selectedImages.length})',
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              TextButton(
                                onPressed: () {
                                  setState(() {
                                    _selectedImages.clear();
                                  });
                                },
                                child: const Text('Clear All'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          SizedBox(
                            height: 100,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: _selectedImages.length,
                              itemBuilder: (context, index) {
                                return Container(
                                  margin: const EdgeInsets.only(right: 8),
                                  width: 100,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: AppTheme.dividerColor,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.file(
                                      File(_selectedImages[index].path),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Title Field (only for single image)
                if (_selectedImages.length == 1) ...[
                  Text(
                    'Wallpaper Title',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      hintText: 'Enter wallpaper title (optional)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.title),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Category Selection
                Text(
                  'Category',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 8),

                Row(
                  children: [
                    Radio<bool>(
                      value: false,
                      groupValue: _isNewCategory,
                      onChanged: (value) {
                        setState(() {
                          _isNewCategory = value!;
                        });
                      },
                    ),
                    const Text('Existing Category'),
                    const SizedBox(width: 24),
                    Radio<bool>(
                      value: true,
                      groupValue: _isNewCategory,
                      onChanged: (value) {
                        setState(() {
                          _isNewCategory = value!;
                        });
                      },
                    ),
                    const Text('New Category'),
                  ],
                ),

                const SizedBox(height: 12),

                if (_isNewCategory)
                  TextFormField(
                    controller: _categoryController,
                    decoration: const InputDecoration(
                      hintText: 'Enter new category name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.category),
                    ),
                    validator: (value) {
                      if (_isNewCategory &&
                          (value == null || value.trim().isEmpty)) {
                        return 'Please enter a category name';
                      }
                      return null;
                    },
                  )
                else
                  Consumer<WallpaperProvider>(
                    builder: (context, provider, child) {
                      // Get existing categories from wallpapers
                      final existingCategories = provider.categories
                          .where((cat) => cat.name != 'All')
                          .map((cat) => cat.name)
                          .toList();

                      // Combine with predefined categories
                      final allCategories = {
                        ..._predefinedCategories,
                        ...existingCategories,
                      }.toList();

                      // Ensure selected category exists in the list
                      if (!allCategories.contains(_selectedCategory)) {
                        _selectedCategory = allCategories.isNotEmpty
                            ? allCategories.first
                            : 'Nature';
                      }

                      return DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.category),
                        ),
                        items: allCategories.map((category) {
                          return DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      );
                    },
                  ),

                const SizedBox(height: 24),

                // Premium Wallpaper Checkbox
                Row(
                  children: [
                    Checkbox(
                      value: _isPremiumWallpaper,
                      onChanged: (value) {
                        setState(() {
                          _isPremiumWallpaper = value ?? false;
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Mark as Premium Wallpaper',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Icon(Icons.star, color: Colors.amber, size: 20),
                  ],
                ),

                const SizedBox(height: 32),

                // Upload Button
                Consumer<WallpaperProvider>(
                  builder: (context, wallpaperProvider, child) {
                    return SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: wallpaperProvider.isLoading
                            ? null
                            : _uploadWallpapers,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryBlue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: wallpaperProvider.isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.cloud_upload),
                                  const SizedBox(width: 8),
                                  Text(
                                    _selectedImages.length > 1
                                        ? 'Upload ${_selectedImages.length} Wallpapers'
                                        : 'Upload Wallpaper',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 24),

                // Send Notification Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryGreen.withValues(
                                  alpha: 0.1,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Icons.notifications_active,
                                color: AppTheme.primaryGreen,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Send Notifications',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleLarge,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Send push notifications to users',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        // Notification Target Selection
                        Text(
                          'Send To:',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _selectedNotificationTarget,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          items: [
                            const DropdownMenuItem(
                              value: 'all_users',
                              child: Text('All Users'),
                            ),
                            ...AdminNotificationService.getAvailableTopics()
                                .where((topic) => topic != 'all_users')
                                .map(
                                  (topic) => DropdownMenuItem(
                                    value: topic,
                                    child: Text(
                                      topic.replaceAll('_', ' ').toUpperCase(),
                                    ),
                                  ),
                                ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedNotificationTarget = value!;
                            });
                          },
                        ),

                        const SizedBox(height: 16),

                        // Notification Title
                        TextFormField(
                          controller: _notificationTitleController,
                          decoration: InputDecoration(
                            labelText: 'Notification Title',
                            hintText: 'Enter notification title',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Notification Body
                        TextFormField(
                          controller: _notificationBodyController,
                          maxLines: 3,
                          decoration: InputDecoration(
                            labelText: 'Notification Message',
                            hintText: 'Enter notification message',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Please enter a message';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Notification Image URL (Optional)
                        TextFormField(
                          controller: _notificationImageUrlController,
                          decoration: InputDecoration(
                            labelText: 'Image URL (Optional)',
                            hintText: 'Enter image URL for rich notification',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            prefixIcon: const Icon(Icons.image),
                            suffixIcon:
                                _notificationImageUrlController.text.isNotEmpty
                                ? IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      setState(() {
                                        _notificationImageUrlController.clear();
                                      });
                                    },
                                  )
                                : null,
                          ),
                          validator: (value) {
                            if (value != null && value.trim().isNotEmpty) {
                              // Basic URL validation
                              final uri = Uri.tryParse(value.trim());
                              if (uri == null || !uri.hasAbsolutePath) {
                                return 'Please enter a valid URL';
                              }
                            }
                            return null;
                          },
                          onChanged: (value) {
                            setState(
                              () {},
                            ); // Refresh to show/hide clear button
                          },
                        ),

                        const SizedBox(height: 20),

                        // Send Notification Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _isSendingNotification
                                ? null
                                : _sendNotification,
                            icon: _isSendingNotification
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Icon(Icons.send),
                            label: Text(
                              _isSendingNotification
                                  ? 'Sending...'
                                  : 'Send Notification',
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryGreen,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 12),

                        // Test Notification Button
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: _isSendingNotification
                                ? null
                                : _sendTestNotification,
                            icon: const Icon(Icons.bug_report),
                            label: const Text('Send Test Notification'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.primaryBlue,
                              side: BorderSide(color: AppTheme.primaryBlue),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Instructions Card
                Card(
                  color: AppTheme.primaryBlue.withValues(alpha: 0.1),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: AppTheme.primaryBlue,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Instructions',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(color: AppTheme.primaryBlue),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          '• Select high-quality images (preferably 4K resolution)\n'
                          '• For bulk upload, select multiple images of the same category\n'
                          '• Images will be automatically optimized for different screen sizes\n'
                          '• New categories will be created automatically in Cloudinary\n'
                          '• Send notifications to inform users about new content\n'
                          '• Notifications will show the app icon in the status bar',
                          style: TextStyle(height: 1.5),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
