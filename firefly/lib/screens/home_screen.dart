import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../providers/wallpaper_provider.dart';
import '../providers/auth_provider.dart';
import '../models/wallpaper_model.dart';
import '../models/category_model.dart';
import '../utils/app_theme.dart';
import '../widgets/app_drawer.dart';
import '../services/notification_service.dart';
import '../services/analytics_service.dart';

import 'wallpaper_preview_screen.dart';
import 'admin_panel_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  bool _isScrolling = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadWallpapers();
      _setStatusBarStyle();
      // Set context for notification service
      NotificationService.setContext(context);
      // Track screen view (works even without auth)
      try {
        AnalyticsService.trackScreenView(screenName: 'home_screen');
      } catch (e) {
        print('Analytics tracking failed (no auth): $e');
      }
    });
  }

  void _onScroll() {
    if (_scrollController.position.userScrollDirection ==
            ScrollDirection.forward ||
        _scrollController.position.userScrollDirection ==
            ScrollDirection.reverse) {
      if (!_isScrolling) {
        setState(() {
          _isScrolling = true;
        });
        _hideStatusBar();

        // Show status bar again after scroll stops
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted) {
            setState(() {
              _isScrolling = false;
            });
            _showStatusBar();
          }
        });
      }
    }
  }

  void _setStatusBarStyle() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
    );
  }

  void _hideStatusBar() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  void _showStatusBar() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    _setStatusBarStyle();
  }

  Future<void> _loadWallpapers() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    await wallpaperProvider.loadWallpapers();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      drawer: const AppDrawer(),
      body: RefreshIndicator(
        onRefresh: _loadWallpapers,
        color: const Color(0xFFFF6B35),
        backgroundColor: const Color(0xFF1A1A1A),
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF1A1A1A), Color(0xFF0A0A0A)],
                    ),
                  ),
                ),
                title: const Text(
                  'FireFly',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.w300,
                    letterSpacing: 2.0,
                  ),
                ),
                centerTitle: true,
              ),
              leading: Builder(
                builder: (context) => IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.menu,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              ),
              actions: [
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    if (authProvider.isAdmin) {
                      return Padding(
                        padding: const EdgeInsets.only(right: 16),
                        child: IconButton(
                          icon: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFFFF6B35,
                              ).withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.admin_panel_settings,
                              color: Color(0xFFFF6B35),
                              size: 20,
                            ),
                          ),
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const AdminPanelScreen(),
                              ),
                            );
                          },
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                child: Consumer<WallpaperProvider>(
                  builder: (context, wallpaperProvider, child) {
                    // Refresh categories if empty
                    if (wallpaperProvider.categories.isEmpty) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        wallpaperProvider.loadWallpapers();
                      });
                    }

                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1A1A1A),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.1),
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: wallpaperProvider.selectedCategory,
                          isExpanded: true,
                          icon: const Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white70,
                          ),
                          dropdownColor: const Color(0xFF1A1A1A),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                          ),
                          items: wallpaperProvider.categories
                              .map(
                                (category) => DropdownMenuItem(
                                  value: category.name == 'All'
                                      ? null
                                      : category.name,
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.category_outlined,
                                        color: Colors.white70,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        '${category.name} (${category.count})',
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              .toList(),
                          onChanged: (value) {
                            wallpaperProvider.setSelectedCategory(value);
                          },
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // Banner Ad after category selection
            Consumer<WallpaperProvider>(
              builder: (context, wallpaperProvider, child) {
                if (wallpaperProvider.isLoading &&
                    wallpaperProvider.wallpapers.isEmpty) {
                  return SliverToBoxAdapter(
                    child: SizedBox(
                      height: 200,
                      child: const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFFFF6B35),
                          ),
                          strokeWidth: 2,
                        ),
                      ),
                    ),
                  );
                }

                if (wallpaperProvider.errorMessage != null) {
                  return SliverToBoxAdapter(
                    child: _buildErrorWidget(wallpaperProvider.errorMessage!),
                  );
                }

                if (wallpaperProvider.wallpapers.isEmpty) {
                  return SliverToBoxAdapter(child: _buildEmptyWidget());
                }

                return SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverGrid(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 20,
                          mainAxisSpacing: 20,
                          childAspectRatio: 9 / 16,
                        ),
                    delegate: SliverChildBuilderDelegate((context, index) {
                      // Show wallpaper directly without ad logic for now
                      if (index < wallpaperProvider.wallpapers.length) {
                        final wallpaper = wallpaperProvider.wallpapers[index];
                        return _buildWallpaperCard(wallpaper);
                      }

                      // Return empty container if no more wallpapers
                      return const SizedBox.shrink();
                    }, childCount: wallpaperProvider.wallpapers.length),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 9 / 16, // Instagram Reels aspect ratio
        ),
        itemCount: 10,
        itemBuilder: (context, index) {
          return Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.4),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadWallpapers,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Consumer<WallpaperProvider>(
      builder: (context, wallpaperProvider, child) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.image_not_supported_outlined,
                  size: 64,
                  color: AppTheme.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  'No wallpapers found',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  wallpaperProvider.selectedCategory == null
                      ? 'Check back later for new wallpapers'
                      : 'No wallpapers in "${wallpaperProvider.selectedCategory}" category',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Debug Info:',
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Selected category: ${wallpaperProvider.selectedCategory ?? "All"}\n'
                  'Filtered wallpapers: ${wallpaperProvider.wallpapers.length}\n'
                  'Categories: ${wallpaperProvider.categories.map((c) => c.name).join(", ")}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadWallpapers,
                  child: const Text('Refresh'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWallpaperGrid(List wallpapers) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        controller: _scrollController,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 9 / 16, // Instagram Reels aspect ratio
        ),
        itemCount: wallpapers.length,
        itemBuilder: (context, index) {
          final wallpaper = wallpapers[index];
          return _buildWallpaperCard(wallpaper);
        },
      ),
    );
  }

  Widget _buildWallpaperCard(dynamic wallpaper) {
    // Fix duplicate folder path in URL
    String imageUrl = wallpaper.imageUrl;
    if (imageUrl.contains('/nature/nature/')) {
      imageUrl = imageUrl.replaceAll('/nature/nature/', '/nature/');
    }
    if (imageUrl.contains('/abstract/abstract/')) {
      imageUrl = imageUrl.replaceAll('/abstract/abstract/', '/abstract/');
    }
    if (imageUrl.contains('/cars/cars/')) {
      imageUrl = imageUrl.replaceAll('/cars/cars/', '/cars/');
    }
    if (imageUrl.contains('/galaxy/galaxy/')) {
      imageUrl = imageUrl.replaceAll('/galaxy/galaxy/', '/galaxy/');
    }
    if (imageUrl.contains('/animals/animals/')) {
      imageUrl = imageUrl.replaceAll('/animals/animals/', '/animals/');
    }
    if (imageUrl.contains('/architecture/architecture/')) {
      imageUrl = imageUrl.replaceAll(
        '/architecture/architecture/',
        '/architecture/',
      );
    }
    if (imageUrl.contains('/technology/technology/')) {
      imageUrl = imageUrl.replaceAll('/technology/technology/', '/technology/');
    }
    if (imageUrl.contains('/art/art/')) {
      imageUrl = imageUrl.replaceAll('/art/art/', '/art/');
    }
    if (imageUrl.contains('/minimalist/minimalist/')) {
      imageUrl = imageUrl.replaceAll('/minimalist/minimalist/', '/minimalist/');
    }
    if (imageUrl.contains('/dark/dark/')) {
      imageUrl = imageUrl.replaceAll('/dark/dark/', '/dark/');
    }
    if (imageUrl.contains('/amoled/amoled/')) {
      imageUrl = imageUrl.replaceAll('/amoled/amoled/', '/amoled/');
    }
    if (imageUrl.contains('/darkverse/darkverse/')) {
      imageUrl = imageUrl.replaceAll('/darkverse/darkverse/', '/darkverse/');
    }
    if (imageUrl.contains('/typography/typography/')) {
      imageUrl = imageUrl.replaceAll('/typography/typography/', '/typography/');
    }
    if (imageUrl.contains('/text/text/')) {
      imageUrl = imageUrl.replaceAll('/text/text/', '/text/');
    }

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (_) => WallpaperPreviewScreen(wallpaper: wallpaper),
          ),
        );
      },
      child: Hero(
        tag: 'wallpaper_${wallpaper.id}',
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              fit: StackFit.expand,
              children: [
                CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  memCacheWidth: 400, // Reduce memory cache size
                  memCacheHeight: 600, // Reduce memory cache size
                  maxWidthDiskCache: 400, // Reduce disk cache size
                  maxHeightDiskCache: 600, // Reduce disk cache size
                  placeholder: (context, url) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF1A1A1A),
                          const Color(0xFF2A2A2A),
                        ],
                      ),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFFFF6B35),
                        ),
                        strokeWidth: 2,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF1A1A1A),
                          const Color(0xFF2A2A2A),
                        ],
                      ),
                    ),
                    child: const Icon(
                      Icons.broken_image_outlined,
                      color: Colors.white30,
                      size: 48,
                    ),
                  ),
                ),

                // Gradient overlay
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 100,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.9),
                        ],
                      ),
                    ),
                  ),
                ),

                // Clean wallpaper view without category labels

                // Category label
                Positioned(
                  bottom: 12,
                  left: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      wallpaper.category,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),

                // Favorite button
                Positioned(
                  top: 12,
                  right: 12,
                  child: Consumer<WallpaperProvider>(
                    builder: (context, provider, child) {
                      final isFavorite = provider.isFavorite(wallpaper.id);
                      return GestureDetector(
                        onTap: () => provider.toggleFavorite(wallpaper.id),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.1),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? Colors.red : Colors.white,
                            size: 18,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
