import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:wallpaper_manager_flutter/wallpaper_manager_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:gal/gal.dart';
import 'dart:io';
import '../utils/app_theme.dart';
import '../providers/auth_provider.dart';
import '../providers/wallpaper_provider.dart';
import '../services/analytics_service.dart';
import '../services/user_management_service.dart';
import '../services/rating_prompt_service.dart';
import '../widgets/app_sharing_widget.dart';

class WallpaperPreviewScreen extends StatefulWidget {
  final dynamic wallpaper;

  const WallpaperPreviewScreen({super.key, required this.wallpaper});

  @override
  State<WallpaperPreviewScreen> createState() => _WallpaperPreviewScreenState();
}

class _WallpaperPreviewScreenState extends State<WallpaperPreviewScreen>
    with TickerProviderStateMixin {
  late AnimationController _buttonController;
  late Animation<double> _buttonAnimation;
  bool _isLoading = false;
  bool _showButtons = true;
  int _viewCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _incrementViewCount();
    _loadViewCount();
    _trackWallpaperView();
  }

  // Track wallpaper view for analytics
  void _trackWallpaperView() {
    try {
      AnalyticsService.trackWallpaperView(
        wallpaperId: widget.wallpaper.id,
        category: widget.wallpaper.category,
        source: 'preview_screen',
      );

      // Update user stats only if user is authenticated
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      if (authProvider.user != null) {
        UserManagementService.updateUserStats(
          userId: authProvider.user!.uid,
          incrementField: 'wallpapersViewed',
        );
      } else {
        print('📊 Wallpaper view tracked without user stats (no auth)');
      }
    } catch (e) {
      print('Error tracking wallpaper view: $e');
    }
  }

  void _initializeAnimations() {
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _buttonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _buttonController, curve: Curves.easeInOut),
    );

    _buttonController.forward();
  }

  @override
  void dispose() {
    _buttonController.dispose();
    super.dispose();
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: const Text(
            'Delete Wallpaper',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'Are you sure you want to permanently delete this wallpaper? This action cannot be undone.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteWallpaper();
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteWallpaper() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final wallpaperProvider = Provider.of<WallpaperProvider>(
        context,
        listen: false,
      );

      if (authProvider.user?.email == null) {
        _showSnackBar('Error: User not authenticated', isError: true);
        return;
      }

      // Extract public_id from the wallpaper URL
      final publicId = wallpaperProvider.extractPublicIdFromUrl(
        widget.wallpaper.imageUrl,
      );

      final success = await wallpaperProvider.deleteWallpaper(
        wallpaperId: widget.wallpaper.id,
        publicId: publicId,
        userEmail: authProvider.user!.email!,
      );

      if (success) {
        _showSnackBar('Wallpaper deleted successfully');
        // Navigate back to home screen
        if (mounted) {
          Navigator.of(context).pop();
        }
      } else {
        _showSnackBar(
          wallpaperProvider.errorMessage ?? 'Failed to delete wallpaper',
          isError: true,
        );
      }
    } catch (e) {
      _showSnackBar('Error: ${e.toString()}', isError: true);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _toggleButtons() {
    setState(() {
      _showButtons = !_showButtons;
      if (_showButtons) {
        _buttonController.forward();
      } else {
        _buttonController.reverse();
      }
    });
  }

  Future<void> _downloadWallpaper() async {
    // Direct download without ads
    await _performDownload();
  }

  Future<void> _performDownload() async {
    try {
      setState(() => _isLoading = true);

      // Request storage permission based on Android version
      bool hasPermission = false;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          // Android 13+ (API 33+) - Request media permissions
          final permission = await Permission.photos.request();
          hasPermission = permission.isGranted;
        } else if (androidInfo.version.sdkInt >= 30) {
          // Android 11+ (API 30+) - Request manage external storage
          final permission = await Permission.manageExternalStorage.request();
          hasPermission = permission.isGranted;
        } else {
          // Android 10 and below - Request storage permission
          final permission = await Permission.storage.request();
          hasPermission = permission.isGranted;
        }
      } else {
        // iOS - Request photos permission
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
      }

      if (!hasPermission) {
        _showSnackBar(
          'Storage permission is required to download wallpapers',
          isError: true,
        );
        return;
      }

      // Get the image URL
      final String downloadUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );

      // Download image to memory first
      final dio = Dio();
      final response = await dio.get(
        downloadUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // Save image to gallery using Gal
      await Gal.putImageBytes(response.data, album: "FireFly Wallpapers");

      _showSnackBar('Wallpaper saved to gallery successfully!');

      // Track download analytics
      try {
        await AnalyticsService.trackWallpaperDownload(
          wallpaperId: widget.wallpaper.id,
          category: widget.wallpaper.category,
          quality: 'standard',
          source: 'preview_screen',
        );

        // Track download for rating prompt
        await RatingPromptService.trackWallpaperDownload();

        // Update user stats (get auth provider before async operations)
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          final user = authProvider.user;
          if (user != null) {
            await UserManagementService.updateUserStats(
              userId: user.uid,
              incrementField: 'wallpapersDownloaded',
            );
          }
        }
      } catch (e) {
        print('Error tracking download: $e');
      }
    } catch (e) {
      _showSnackBar(
        'Failed to download wallpaper: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _setWallpaper(int location) async {
    // Show perfect fit tips dialog first
    await _showPerfectFitTips(location);
  }

  Future<void> _showPerfectFitTips(int location) async {
    String locationText = '';
    switch (location) {
      case 1:
        locationText = 'Home Screen';
        break;
      case 2:
        locationText = 'Lock Screen';
        break;
      case 3:
        locationText = 'Both Screens';
        break;
    }

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1E1E1E),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            const Icon(Icons.tips_and_updates, color: Colors.amber, size: 24),
            const SizedBox(width: 8),
            const Text(
              'Perfect Fit Tips',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setting wallpaper for: $locationText',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '📱 Perfect Fit Tips:',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '• Wallpaper ka resolution device screen ke equal ya usse zyada ho',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Example: 1080x1920 (portrait phones), 1440x2560, etc.',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '• Image ko center-crop ya center-fit karke adjust karo',
              style: TextStyle(color: Colors.white70, fontSize: 14),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Yeh wallpaper automatically adjust ho jayega aapke device ke liye!',
                      style: TextStyle(color: Colors.blue, fontSize: 13),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _proceedWithWallpaperSetting(location);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF1976D2),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Set Wallpaper',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _proceedWithWallpaperSetting(int location) async {
    // Direct wallpaper setting without ads
    await _performSetWallpaper(location);
  }

  Future<void> _performSetWallpaper(int location) async {
    try {
      setState(() => _isLoading = true);

      final String wallpaperUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );
      String result = 'Failed';

      // Download the image first, then set as wallpaper
      final response = await Dio().get(
        wallpaperUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      final directory = await getTemporaryDirectory();
      final file = File(
        '${directory.path}/wallpaper_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );
      await file.writeAsBytes(response.data);

      switch (location) {
        case 1: // Home Screen
          result =
              await WallpaperManagerFlutter().setWallpaper(
                file,
                WallpaperManagerFlutter.homeScreen,
              )
              ? 'Wallpaper set'
              : 'Failed';
          break;
        case 2: // Lock Screen
          result =
              await WallpaperManagerFlutter().setWallpaper(
                file,
                WallpaperManagerFlutter.lockScreen,
              )
              ? 'Wallpaper set'
              : 'Failed';
          break;
        case 3: // Both
          result =
              await WallpaperManagerFlutter().setWallpaper(
                file,
                WallpaperManagerFlutter.bothScreens,
              )
              ? 'Wallpaper set'
              : 'Failed';
          break;
        default:
          result = 'Invalid location';
      }

      // Clean up temporary file
      if (await file.exists()) {
        await file.delete();
      }

      if (result == 'Wallpaper set') {
        _showSnackBar('Wallpaper set successfully!');
      } else {
        _showSnackBar('Failed to set wallpaper', isError: true);
      }
    } catch (e) {
      _showSnackBar('Failed to set wallpaper: ${e.toString()}', isError: true);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _downloadHDWallpaper() async {
    // Check if wallpaper is premium and show premium access reward first
    // Direct HD download without ads
    await _performHDDownload();
  }

  Future<void> _performHDDownload() async {
    try {
      setState(() => _isLoading = true);

      // Request storage permission
      bool hasPermission = false;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;

        if (androidInfo.version.sdkInt >= 33) {
          final permission = await Permission.photos.request();
          hasPermission = permission.isGranted;
        } else if (androidInfo.version.sdkInt >= 30) {
          final permission = await Permission.manageExternalStorage.request();
          hasPermission = permission.isGranted;
        } else {
          final permission = await Permission.storage.request();
          hasPermission = permission.isGranted;
        }
      } else {
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
      }

      if (!hasPermission) {
        _showSnackBar(
          'Storage permission is required to download wallpapers',
          isError: true,
        );
        return;
      }

      // Use HD URL if available, otherwise use regular URL
      final String downloadUrl = _getFixedImageUrl(
        widget.wallpaper.hdUrl.isNotEmpty
            ? widget.wallpaper.hdUrl
            : widget.wallpaper.imageUrl,
      );

      // Download image to memory first
      final dio = Dio();
      final response = await dio.get(
        downloadUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      // Save to gallery with HD prefix
      final fileName =
          'FireFly_HD_${widget.wallpaper.title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.jpg';

      await Gal.putImageBytes(response.data, name: fileName);

      _showSnackBar('HD Wallpaper downloaded successfully! ✨');

      // Track HD download analytics
      try {
        await AnalyticsService.trackWallpaperDownload(
          wallpaperId: widget.wallpaper.id,
          category: widget.wallpaper.category,
          quality: 'hd',
          source: 'preview_screen',
        );

        // Track download for rating prompt
        await RatingPromptService.trackWallpaperDownload();

        // Update user stats (get auth provider before async operations)
        if (mounted) {
          final authProvider = Provider.of<AuthProvider>(
            context,
            listen: false,
          );
          final user = authProvider.user;
          if (user != null) {
            await UserManagementService.updateUserStats(
              userId: user.uid,
              incrementField: 'wallpapersDownloaded',
            );
          }
        }
      } catch (e) {
        print('Error tracking HD download: $e');
      }
    } catch (e) {
      _showSnackBar(
        'Failed to download HD wallpaper: ${e.toString()}',
        isError: true,
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  String _getFixedImageUrl(String url) {
    // Fix duplicate folder paths in URL
    String fixedUrl = url;
    if (fixedUrl.contains('/nature/nature/')) {
      fixedUrl = fixedUrl.replaceAll('/nature/nature/', '/nature/');
    }
    if (fixedUrl.contains('/abstract/abstract/')) {
      fixedUrl = fixedUrl.replaceAll('/abstract/abstract/', '/abstract/');
    }
    if (fixedUrl.contains('/cars/cars/')) {
      fixedUrl = fixedUrl.replaceAll('/cars/cars/', '/cars/');
    }
    if (fixedUrl.contains('/galaxy/galaxy/')) {
      fixedUrl = fixedUrl.replaceAll('/galaxy/galaxy/', '/galaxy/');
    }
    if (fixedUrl.contains('/architecture/architecture/')) {
      fixedUrl = fixedUrl.replaceAll(
        '/architecture/architecture/',
        '/architecture/',
      );
    }
    if (fixedUrl.contains('/animals/animals/')) {
      fixedUrl = fixedUrl.replaceAll('/animals/animals/', '/animals/');
    }
    if (fixedUrl.contains('/technology/technology/')) {
      fixedUrl = fixedUrl.replaceAll('/technology/technology/', '/technology/');
    }
    if (fixedUrl.contains('/art/art/')) {
      fixedUrl = fixedUrl.replaceAll('/art/art/', '/art/');
    }
    if (fixedUrl.contains('/minimalist/minimalist/')) {
      fixedUrl = fixedUrl.replaceAll('/minimalist/minimalist/', '/minimalist/');
    }
    if (fixedUrl.contains('/dark/dark/')) {
      fixedUrl = fixedUrl.replaceAll('/dark/dark/', '/dark/');
    }
    if (fixedUrl.contains('/amoled/amoled/')) {
      fixedUrl = fixedUrl.replaceAll('/amoled/amoled/', '/amoled/');
    }
    if (fixedUrl.contains('/darkverse/darkverse/')) {
      fixedUrl = fixedUrl.replaceAll('/darkverse/darkverse/', '/darkverse/');
    }
    if (fixedUrl.contains('/typography/typography/')) {
      fixedUrl = fixedUrl.replaceAll('/typography/typography/', '/typography/');
    }
    if (fixedUrl.contains('/text/text/')) {
      fixedUrl = fixedUrl.replaceAll('/text/text/', '/text/');
    }
    return fixedUrl;
  }

  @override
  Widget build(BuildContext context) {
    final String fixedImageUrl = _getFixedImageUrl(
      widget.wallpaper.hdUrl.isNotEmpty
          ? widget.wallpaper.hdUrl
          : widget.wallpaper.imageUrl,
    );

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Wallpaper Image
          GestureDetector(
            onTap: _toggleButtons,
            child: Hero(
              tag: 'wallpaper_${widget.wallpaper.id}',
              child: PhotoView(
                imageProvider: CachedNetworkImageProvider(fixedImageUrl),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                backgroundDecoration: const BoxDecoration(color: Colors.black),
                loadingBuilder: (context, event) => Center(
                  child: CircularProgressIndicator(
                    value: event == null
                        ? 0
                        : event.cumulativeBytesLoaded /
                              event.expectedTotalBytes!,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white,
                    ),
                  ),
                ),
                errorBuilder: (context, error, stackTrace) => const Center(
                  child: Icon(
                    Icons.broken_image,
                    color: Colors.white,
                    size: 64,
                  ),
                ),
              ),
            ),
          ),

          // Top App Bar
          AnimatedBuilder(
            animation: _buttonAnimation,
            builder: (context, child) {
              return Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Transform.translate(
                  offset: Offset(0, -60 * (1 - _buttonAnimation.value)),
                  child: Container(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top,
                      left: 16,
                      right: 16,
                      bottom: 16,
                    ),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.7),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.wallpaper.title,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Row(
                                children: [
                                  Text(
                                    widget.wallpaper.category,
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Icon(
                                    Icons.visibility,
                                    color: Colors.white70,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '$_viewCount views',
                                    style: const TextStyle(
                                      color: Colors.white70,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            // Share functionality can be added here
                          },
                          icon: const Icon(Icons.share, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),

          // Bottom Action Buttons
          AnimatedBuilder(
            animation: _buttonAnimation,
            builder: (context, child) {
              return Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 16,
                    top: 24,
                    bottom: MediaQuery.of(context).padding.bottom + 16,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.download,
                              label: 'Download',
                              onPressed: _isLoading ? null : _downloadWallpaper,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.hd,
                              label: 'HD Download',
                              onPressed: _isLoading
                                  ? null
                                  : _downloadHDWallpaper,
                              isPremium: true,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.home,
                              label: 'Home Screen',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(1),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.lock,
                              label: 'Lock Screen',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(2),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.wallpaper,
                              label: 'Both Screens',
                              onPressed: _isLoading
                                  ? null
                                  : () => _setWallpaper(3),
                              isPrimary: true,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.share,
                              label: 'Share',
                              onPressed: _isLoading ? null : _shareWallpaper,
                            ),
                          ),
                        ],
                      ),
                      // Admin delete button
                      Consumer<AuthProvider>(
                        builder: (context, authProvider, child) {
                          if (authProvider.isAdmin) {
                            return Column(
                              children: [
                                const SizedBox(height: 12),
                                SizedBox(
                                  width: double.infinity,
                                  child: _buildActionButton(
                                    icon: Icons.delete_forever,
                                    label: 'Delete Wallpaper (Admin)',
                                    onPressed: _isLoading
                                        ? null
                                        : _showDeleteConfirmation,
                                    isDelete: true,
                                  ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          // Loading Overlay
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    bool isPrimary = false,
    bool isDelete = false,
    bool isPremium = false,
  }) {
    Color backgroundColor;
    Color foregroundColor;

    if (isDelete) {
      backgroundColor = Colors.red.withValues(alpha: 0.9);
      foregroundColor = Colors.white;
    } else if (isPremium) {
      backgroundColor = const Color(
        0xFFFFD700,
      ).withValues(alpha: 0.9); // Gold color for premium
      foregroundColor = Colors.black;
    } else if (isPrimary) {
      backgroundColor = AppTheme.primaryBlue;
      foregroundColor = Colors.white;
    } else {
      backgroundColor = Colors.white.withValues(alpha: 0.9);
      foregroundColor = Colors.black;
    }

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 4,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Icon(icon, size: 24),
              if (isPremium)
                Positioned(
                  right: -2,
                  top: -2,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFD700),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.star, size: 8, color: Colors.black),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _incrementViewCount() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    await wallpaperProvider.incrementViewCount(widget.wallpaper.id);
  }

  Future<void> _loadViewCount() async {
    final wallpaperProvider = Provider.of<WallpaperProvider>(
      context,
      listen: false,
    );
    final viewCount = await wallpaperProvider.getViewCount(widget.wallpaper.id);
    if (mounted) {
      setState(() {
        _viewCount = viewCount;
      });
    }
  }
}
