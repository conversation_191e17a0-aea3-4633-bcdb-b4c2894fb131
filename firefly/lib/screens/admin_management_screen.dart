import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/admin_config_provider.dart';
import '../providers/auth_provider.dart';

class AdminManagementScreen extends StatefulWidget {
  const AdminManagementScreen({super.key});

  @override
  State<AdminManagementScreen> createState() => _AdminManagementScreenState();
}

class _AdminManagementScreenState extends State<AdminManagementScreen> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Management'),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Consumer2<AdminConfigProvider, AuthProvider>(
        builder: (context, adminConfig, authProvider, child) {
          if (!authProvider.isAdmin) {
            return const Center(
              child: Text(
                'Access Denied: Admin privileges required',
                style: TextStyle(fontSize: 18, color: Colors.red),
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Add Admin Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Add New Admin',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Form(
                          key: _formKey,
                          child: Row(
                            children: [
                              Expanded(
                                child: TextFormField(
                                  controller: _emailController,
                                  decoration: const InputDecoration(
                                    labelText: 'Email Address',
                                    hintText: 'Enter admin email',
                                    border: OutlineInputBorder(),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter an email';
                                    }
                                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value)) {
                                      return 'Please enter a valid email';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              const SizedBox(width: 16),
                              ElevatedButton(
                                onPressed: adminConfig.isLoading
                                    ? null
                                    : () => _addAdmin(context),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.deepPurple,
                                  foregroundColor: Colors.white,
                                ),
                                child: adminConfig.isLoading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                              Colors.white),
                                        ),
                                      )
                                    : const Text('Add Admin'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                
                // Current Admins Section
                const Text(
                  'Current Admins',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Error Message
                if (adminConfig.errorMessage != null)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      border: Border.all(color: Colors.red.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.error, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            adminConfig.errorMessage!,
                            style: TextStyle(color: Colors.red.shade700),
                          ),
                        ),
                        IconButton(
                          onPressed: () => adminConfig.clearError(),
                          icon: const Icon(Icons.close),
                          iconSize: 20,
                        ),
                      ],
                    ),
                  ),
                
                // Admin List
                Expanded(
                  child: adminConfig.isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : ListView.builder(
                          itemCount: adminConfig.adminEmails.length,
                          itemBuilder: (context, index) {
                            final email = adminConfig.adminEmails[index];
                            final isCurrentUser = email.toLowerCase() == 
                                authProvider.user?.email?.toLowerCase();
                            
                            return Card(
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: isCurrentUser 
                                      ? Colors.deepPurple 
                                      : Colors.grey.shade400,
                                  child: Icon(
                                    isCurrentUser ? Icons.person : Icons.admin_panel_settings,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(email),
                                subtitle: isCurrentUser 
                                    ? const Text('You', style: TextStyle(color: Colors.deepPurple))
                                    : null,
                                trailing: adminConfig.adminEmails.length > 1 && !isCurrentUser
                                    ? IconButton(
                                        onPressed: () => _showRemoveConfirmation(context, email),
                                        icon: const Icon(Icons.remove_circle, color: Colors.red),
                                        tooltip: 'Remove Admin',
                                      )
                                    : null,
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _addAdmin(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    final adminConfig = context.read<AdminConfigProvider>();
    final authProvider = context.read<AuthProvider>();
    
    final success = await adminConfig.addAdminEmail(
      newAdminEmail: _emailController.text.trim(),
      currentUserEmail: authProvider.user?.email ?? '',
    );

    if (success) {
      _emailController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Admin added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _showRemoveConfirmation(BuildContext context, String email) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove Admin'),
          content: Text('Are you sure you want to remove "$email" as an admin?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _removeAdmin(context, email);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _removeAdmin(BuildContext context, String email) async {
    final adminConfig = context.read<AdminConfigProvider>();
    final authProvider = context.read<AuthProvider>();
    
    final success = await adminConfig.removeAdminEmail(
      adminEmailToRemove: email,
      currentUserEmail: authProvider.user?.email ?? '',
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Admin removed successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}