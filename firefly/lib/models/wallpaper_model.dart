class WallpaperModel {
  final String id;
  final String title;
  final String category;
  final String imageUrl;
  final String hdUrl;
  final int timestamp;
  final bool isPremium;

  WallpaperModel({
    required this.id,
    required this.title,
    required this.category,
    required this.imageUrl,
    required this.hdUrl,
    required this.timestamp,
    this.isPremium = false,
  });

  factory WallpaperModel.fromMap(String id, Map<dynamic, dynamic> map) {
    return WallpaperModel(
      id: id,
      title: map['title'] ?? '',
      category: map['category'] ?? '',
      imageUrl: map['imageUrl'] ?? '',
      hdUrl: map['hdUrl'] ?? '',
      timestamp: map['timestamp'] ?? 0,
      isPremium: map['isPremium'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'category': category,
      'imageUrl': imageUrl,
      'hdUrl': hdUrl,
      'timestamp': timestamp,
      'isPremium': isPremium,
    };
  }

  @override
  String toString() {
    return 'WallpaperModel(id: $id, title: $title, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WallpaperModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}