class CategoryModel {
  final String name;
  final int count;

  CategoryModel({
    required this.name,
    required this.count,
  });

  @override
  String toString() {
    return 'CategoryModel(name: $name, count: $count)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.name == name;
  }

  @override
  int get hashCode => name.hashCode;
}