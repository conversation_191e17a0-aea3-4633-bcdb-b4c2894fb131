rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to wallpapers for all users
    match /wallpapers/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow users to read/write their own user documents
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to save their FCM tokens
    match /fcm_tokens/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow anonymous FCM tokens (for users not logged in)
    match /anonymous_tokens/{document} {
      allow read, write: if true;
    }
    
    // Allow reading live notifications for all users
    match /live_notifications/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Allow reading notification history for all users
    match /notification_history/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Admin-only collections (you can customize admin check logic)
    match /admin_config/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Categories collection
    match /categories/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}