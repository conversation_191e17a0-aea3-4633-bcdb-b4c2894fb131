# FireFly Wallpaper App - Ad Implementation Guide

## Overview
This guide covers the complete implementation of Google Mobile Ads (AdMob) in the FireFly wallpaper app, including banner ads, interstitial ads, and rewarded ads with test ad units.

## 🚀 Quick Start

### 1. Dependencies Added
The following dependency has been added to `pubspec.yaml`:
```yaml
dependencies:
  google_mobile_ads: ^5.1.0
```

### 2. Files Created/Modified

#### New Files:
- `lib/services/ad_manager.dart` - Core ad management service
- `lib/config/ad_config.dart` - Ad configuration and settings
- `lib/widgets/banner_ad_widget.dart` - Reusable banner ad widgets
- `lib/widgets/reward_ad_dialog.dart` - Rewarded ad dialog components

#### Modified Files:
- `lib/main.dart` - Added ad initialization
- `lib/screens/home_screen.dart` - Integrated banner ads
- `lib/screens/wallpaper_preview_screen.dart` - Added interstitial and rewarded ads
- `pubspec.yaml` - Added Google Mobile Ads dependency

## 📱 Ad Types Implemented

### 1. Banner Ads
- **Location**: Home screen (after categories and in wallpaper grid)
- **Frequency**: Always visible + every 7th wallpaper in grid
- **Test Ad Unit**: `ca-app-pub-3940256099942544/6300978111`

### 2. Interstitial Ads
- **Triggers**: 
  - Every 3rd download action
  - Every 2nd set wallpaper action
- **Test Ad Unit**: `ca-app-pub-3940256099942544/1033173712`

### 3. Rewarded Ads
- **Purpose**: HD downloads and ad-free sessions
- **Reward**: 30 minutes ad-free experience
- **Test Ad Unit**: `ca-app-pub-3940256099942544/5224354917`

## 🎯 Key Features

### Ad-Free Sessions
- Users can watch rewarded ads to get 30 minutes ad-free
- Automatically granted after watching rewarded ads
- Respects premium user status

### Smart Ad Placement
- Ads respect user preferences (premium status)
- Frequency controls prevent ad spam
- Placement-based configuration

### Error Handling
- Comprehensive error tracking
- Exponential backoff for failed ad loads
- Graceful fallbacks when ads fail

## 🔧 Configuration

### Ad Settings (lib/config/ad_config.dart)
```dart
class AdConfig {
  // Test Ad Unit IDs (replace with real ones for production)
  static const String bannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String interstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
  static const String rewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';
  
  // Ad frequency settings
  static const Duration interstitialCooldown = Duration(minutes: 2);
  static const Duration adFreeSessionDuration = Duration(minutes: 30);
}
```

### Usage Examples

#### Banner Ads
```dart
// Simple banner ad
BannerAdWidget()

// Smart banner (respects premium status)
SmartBannerAdWidget(
  placement: 'home_banner',
  isPremiumUser: false,
)

// Inline banner for lists
InlineBannerAdWidget(
  placement: 'wallpaper_grid',
  frequency: 7,
)
```

#### Interstitial Ads
```dart
// Show interstitial ad
final bool adShown = await AdManager.instance.showInterstitialAd();
if (adShown) {
  // Ad was shown successfully
}
```

#### Rewarded Ads
```dart
// Show rewarded ad dialog
final bool? result = await RewardAdDialogs.showHDDownloadReward(context);
if (result == true) {
  // User watched ad and earned reward
  // Proceed with HD download
}
```

## 🔄 Switching to Production

When ready for production, update the ad unit IDs in `lib/config/ad_config.dart`:

1. **Create AdMob Account**: Sign up at https://admob.google.com
2. **Create App**: Add your app to AdMob
3. **Create Ad Units**: Create banner, interstitial, and rewarded ad units
4. **Update Configuration**:

```dart
class AdConfig {
  // Production Ad Unit IDs
  static const String bannerAdUnitId = 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_BANNER_ID';
  static const String interstitialAdUnitId = 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_INTERSTITIAL_ID';
  static const String rewardedAdUnitId = 'ca-app-pub-YOUR_PUBLISHER_ID/YOUR_REWARDED_ID';
}
```

## 📊 Analytics & Monitoring

The implementation includes:
- Ad impression tracking
- Error logging
- Performance monitoring
- User interaction analytics

## 🛡️ Privacy & Compliance

### GDPR/CCPA Compliance
For production, implement consent management:

```dart
// Add to main.dart before ad initialization
final ConsentInformation consentInfo = ConsentInformation.instance;
await consentInfo.requestConsentInfoUpdate(
  ConsentRequestParameters(),
);

if (consentInfo.consentStatus == ConsentStatus.required) {
  // Show consent form
}
```

### App Store Guidelines
- Test ads are clearly marked
- No misleading ad placements
- Proper user experience maintained

## 🧪 Testing

### Test Ad Units
The app uses Google's official test ad units:
- **Banner**: `ca-app-pub-3940256099942544/6300978111`
- **Interstitial**: `ca-app-pub-3940256099942544/1033173712`
- **Rewarded**: `ca-app-pub-3940256099942544/5224354917`

### Testing Checklist
- [ ] Banner ads load and display correctly
- [ ] Interstitial ads show at appropriate times
- [ ] Rewarded ads grant proper rewards
- [ ] Ad-free sessions work correctly
- [ ] Premium users don't see ads
- [ ] Error handling works gracefully

## 🚨 Important Notes

1. **Test Ads Only**: Current implementation uses test ad units
2. **Production Ready**: Code is production-ready, just needs real ad unit IDs
3. **Performance**: Ads are loaded asynchronously to avoid blocking UI
4. **User Experience**: Frequency controls prevent ad spam
5. **Revenue Optimization**: Strategic placement for maximum engagement

## 📞 Support

For issues or questions:
1. Check AdMob documentation: https://developers.google.com/admob
2. Review Google Mobile Ads Flutter plugin: https://pub.dev/packages/google_mobile_ads
3. Test with different devices and network conditions

## 🔄 Next Steps

1. **Test thoroughly** with the current test ad implementation
2. **Create AdMob account** and app when ready for production
3. **Replace test ad unit IDs** with production IDs
4. **Implement consent management** for GDPR/CCPA compliance
5. **Monitor performance** and optimize ad placements

---

**Ready to monetize your FireFly wallpaper app! 🚀**