#!/bin/bash

# FireFly App - Keystore Creation Script
# Run this script to create a release keystore for Play Store

echo "🔐 Creating Release Keystore for FireFly App"
echo "================================================"

# Set keystore details
KEYSTORE_NAME="firefly-release-key.jks"
KEY_ALIAS="firefly"
VALIDITY_YEARS="25"

echo "📝 Please provide the following information:"
echo ""

# Get user input
read -p "Enter your full name: " FULL_NAME
read -p "Enter your organization (e.g., Anuved): " ORGANIZATION
read -p "Enter your city: " CITY
read -p "Enter your state: " STATE
read -p "Enter your country code (e.g., IN): " COUNTRY
read -s -p "Enter keystore password (remember this!): " STORE_PASSWORD
echo ""
read -s -p "Enter key password (can be same as keystore): " KEY_PASSWORD
echo ""
echo ""

# Create keystore
echo "🔨 Creating keystore..."
keytool -genkey -v \
    -keystore "$KEYSTORE_NAME" \
    -alias "$KEY_ALIAS" \
    -keyalg RSA \
    -keysize 2048 \
    -validity $((365 * VALIDITY_YEARS)) \
    -storepass "$STORE_PASSWORD" \
    -keypass "$KEY_PASSWORD" \
    -dname "CN=$FULL_NAME, OU=$ORGANIZATION, L=$CITY, ST=$STATE, C=$COUNTRY"

if [ $? -eq 0 ]; then
    echo "✅ Keystore created successfully!"
    echo ""
    echo "📋 Important Information:"
    echo "========================"
    echo "Keystore file: $KEYSTORE_NAME"
    echo "Key alias: $KEY_ALIAS"
    echo "Store password: [HIDDEN - Remember this!]"
    echo "Key password: [HIDDEN - Remember this!]"
    echo ""
    echo "⚠️  IMPORTANT: "
    echo "   - Keep this keystore file safe and secure"
    echo "   - Never lose the passwords"
    echo "   - Backup the keystore file"
    echo "   - You cannot update your app without this keystore"
    echo ""
    echo "📁 Next steps:"
    echo "   1. Move keystore to a secure location"
    echo "   2. Update android/key.properties file"
    echo "   3. Update android/app/build.gradle.kts"
    echo "   4. Build release version"
else
    echo "❌ Failed to create keystore"
    exit 1
fi